// Core data types for DishSync application

export interface User {
  id: string;
  email: string;
  name?: string;
  created_at: string;
  updated_at: string;
}

export interface Ingredient {
  id: string;
  name: string;
  unit: string; // e.g., 'cups', 'grams', 'pieces', 'tablespoons'
  category?: string; // e.g., 'vegetables', 'spices', 'proteins'
}

export interface RecipeIngredient {
  id: string;
  recipe_id: string;
  ingredient_id: string;
  quantity: number;
  unit: string;
  notes?: string; // e.g., 'chopped', 'diced', 'room temperature'
  ingredient?: Ingredient;
}

export interface CookingStep {
  id: string;
  recipe_id: string;
  step_number: number;
  title: string;
  description: string;
  duration_minutes?: number; // How long this step takes
  temperature?: number; // Cooking temperature if applicable
  temperature_unit?: 'F' | 'C';
  equipment?: string[]; // e.g., ['oven', 'stovetop', 'mixer']
  is_prep_step: boolean; // True if this is a prep step, false if cooking
  can_be_done_ahead: boolean; // True if this step can be done in advance
  depends_on_steps?: string[]; // IDs of steps that must be completed first
}

export interface Recipe {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  servings: number;
  total_prep_time: number; // in minutes
  total_cook_time: number; // in minutes
  difficulty_level: 1 | 2 | 3 | 4 | 5;
  cuisine_type?: string;
  dietary_tags?: string[]; // e.g., ['vegetarian', 'gluten-free', 'dairy-free']
  created_at: string;
  updated_at: string;
  ingredients: RecipeIngredient[];
  steps: CookingStep[];
}

export interface CookingSession {
  id: string;
  user_id: string;
  name: string;
  recipe_ids: string[];
  target_servings: { [recipe_id: string]: number }; // Scaling factor for each recipe
  start_time?: string;
  estimated_completion_time?: string;
  status: 'planning' | 'prep' | 'cooking' | 'completed' | 'paused';
  created_at: string;
  updated_at: string;
  recipes?: Recipe[];
}

export interface SessionStep {
  id: string;
  session_id: string;
  recipe_id: string;
  step_id: string;
  scheduled_start_time: string; // When this step should start
  scheduled_end_time: string; // When this step should end
  actual_start_time?: string;
  actual_end_time?: string;
  status: 'pending' | 'in_progress' | 'completed' | 'skipped';
  notes?: string;
  step?: CookingStep;
  recipe?: Recipe;
}

export interface Timer {
  id: string;
  session_id: string;
  session_step_id: string;
  name: string;
  duration_minutes: number;
  start_time?: string;
  end_time?: string;
  status: 'ready' | 'running' | 'paused' | 'completed' | 'cancelled';
  sound_enabled: boolean;
  created_at: string;
}

export interface ShoppingListItem {
  id: string;
  session_id: string;
  ingredient_id: string;
  total_quantity: number;
  unit: string;
  is_purchased: boolean;
  notes?: string;
  ingredient?: Ingredient;
}

// UI and state management types
export interface CookingTimeline {
  session_id: string;
  total_duration_minutes: number;
  prep_phase_duration: number;
  cook_phase_duration: number;
  steps: SessionStep[];
  critical_path: string[]; // Step IDs that determine the minimum cooking time
}

export interface DashboardState {
  current_session?: CookingSession;
  active_timers: Timer[];
  current_step?: SessionStep;
  next_steps: SessionStep[];
  mode: 'prep' | 'cook' | 'planning';
}

// Utility types for forms and UI
export interface RecipeFormData {
  name: string;
  description: string;
  servings: number;
  difficulty_level: 1 | 2 | 3 | 4 | 5;
  cuisine_type: string;
  dietary_tags: string[];
  ingredients: Omit<RecipeIngredient, 'id' | 'recipe_id'>[];
  steps: Omit<CookingStep, 'id' | 'recipe_id'>[];
}

export interface SessionFormData {
  name: string;
  recipe_ids: string[];
  target_servings: { [recipe_id: string]: number };
}

// API response types
export interface ApiResponse<T> {
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  count: number;
  page: number;
  per_page: number;
  total_pages: number;
}
