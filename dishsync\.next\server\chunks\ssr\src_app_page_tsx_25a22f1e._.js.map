{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/test/dishsync/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles, X, ChevronUp } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');\n  const [showGettingStarted, setShowGettingStarted] = useState(false);\n  const [showQuickActions, setShowQuickActions] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-yellow-300\">\n      {/* Header */}\n      <header className=\"bg-black border-b-8 border-yellow-400 relative overflow-hidden\">\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-red-500 via-yellow-400 to-blue-500\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            {/* Logo Section */}\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"relative\">\n                <div className=\"bg-yellow-400 border-4 border-white p-3 shadow-[6px_6px_0px_0px_rgba(255,255,255,1)] transform rotate-3 hover:rotate-6 transition-transform\">\n                  <Image\n                    src=\"/assets/logo.png\"\n                    alt=\"DishSync Logo\"\n                    width={56}\n                    height={56}\n                    className=\"rounded-lg\"\n                  />\n                </div>\n              </div>\n              <div className=\"flex flex-col\">\n                <h1 className=\"text-4xl lg:text-5xl font-black text-white tracking-tight transform -rotate-1 hover:rotate-0 transition-transform\">\n                  DISHSYNC\n                </h1>\n                <p className=\"text-yellow-400 font-bold text-sm lg:text-base transform rotate-1 mt-1\">\n                  COOK LIKE A PRO!\n                </p>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex items-center space-x-3\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${\n                  activeTab === 'dashboard'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'\n                }`}\n              >\n                DASHBOARD\n              </button>\n              <button\n                onClick={() => setActiveTab('recipes')}\n                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:-rotate-1 ${\n                  activeTab === 'recipes'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] -rotate-1'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-blue-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'\n                }`}\n              >\n                RECIPES\n              </button>\n              <button\n                onClick={() => setActiveTab('sessions')}\n                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${\n                  activeTab === 'sessions'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-purple-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'\n                }`}\n              >\n                SESSIONS\n              </button>\n\n              {/* Help Button - Special Styling */}\n              <div className=\"relative ml-2\">\n                <button\n                  onClick={() => setShowGettingStarted(true)}\n                  className=\"px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:scale-110 hover:rotate-3 hover:bg-green-400 hover:shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] relative overflow-hidden\"\n                >\n                  <span className=\"relative z-10\">HELP</span>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 opacity-0 hover:opacity-100 transition-opacity\"></div>\n                </button>\n                {/* Pulsing indicator */}\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 border-2 border-white rounded-full animate-pulse\"></div>\n              </div>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {activeTab === 'dashboard' && <DashboardView />}\n        {activeTab === 'recipes' && <RecipesView />}\n        {activeTab === 'sessions' && <SessionsView />}\n      </main>\n\n      {/* Getting Started Modal */}\n      {showGettingStarted && (\n        <div className=\"fixed inset-0 backdrop-blur-md bg-black/20 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-yellow-400 border-8 border-black rounded-3xl shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] w-full max-w-5xl h-[85vh] flex flex-col\">\n            {/* Header */}\n            <div className=\"flex justify-between items-center p-6 border-b-4 border-black flex-shrink-0\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"bg-white border-4 border-black rounded-xl p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n                  <Image\n                    src=\"/assets/logo.png\"\n                    alt=\"DishSync Logo\"\n                    width={32}\n                    height={32}\n                  />\n                </div>\n                <h2 className=\"text-2xl font-black text-black\">\n                  GETTING STARTED GUIDE\n                </h2>\n              </div>\n              <button\n                onClick={() => setShowGettingStarted(false)}\n                className=\"bg-red-500 text-white border-4 border-black rounded-lg p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n              >\n                <X className=\"h-6 w-6\" />\n              </button>\n            </div>\n\n            {/* Content - Flex grow to fill available space */}\n            <div className=\"flex-1 flex flex-col p-6\">\n              <div className=\"text-center mb-6\">\n                <h1 className=\"text-4xl lg:text-5xl font-black text-black mb-3\">\n                  WELCOME TO DISHSYNC!\n                </h1>\n                <h2 className=\"text-xl lg:text-2xl font-bold text-black\">\n                  Your Ultimate Cooking Companion\n                </h2>\n              </div>\n\n              <div className=\"flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 items-center\">\n                {/* Image */}\n                <div className=\"flex justify-center\">\n                  <div className=\"bg-white border-4 border-black rounded-2xl p-4 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)]\">\n                    <Image\n                      src=\"/assets/chef.png\"\n                      alt=\"Chef illustration\"\n                      width={280}\n                      height={280}\n                      className=\"rounded-lg\"\n                    />\n                  </div>\n                </div>\n\n                {/* Content */}\n                <div className=\"space-y-4\">\n                  <div className=\"bg-white border-4 border-black rounded-xl p-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)]\">\n                    <p className=\"text-lg font-bold text-black leading-relaxed\">\n                      DishSync is the REVOLUTIONARY multi-dish cooking assistant that will transform your kitchen experience! Say goodbye to burnt food and timing disasters!\n                    </p>\n                  </div>\n\n                  <div className=\"bg-black border-4 border-white rounded-xl p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                    <h3 className=\"text-white font-black text-lg mb-2\">HOW TO GET STARTED:</h3>\n                    <ul className=\"text-white font-bold space-y-1\">\n                      <li>1. Add your favorite RECIPES</li>\n                      <li>2. Create a COOKING SESSION</li>\n                      <li>3. Follow the PREP MODE</li>\n                      <li>4. Switch to COOK MODE</li>\n                      <li>5. Enjoy PERFECT results!</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Footer */}\n            <div className=\"flex justify-center p-6 border-t-4 border-black flex-shrink-0\">\n              <button\n                onClick={() => setShowGettingStarted(false)}\n                className=\"px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black rounded-xl shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n              >\n                <span className=\"flex items-center gap-2\">START COOKING <ChefHat className=\"h-6 w-6\" /></span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quick Actions - Collapsible from bottom */}\n      <div className={`fixed bottom-0 right-6 z-40 transition-all duration-300 ease-in-out ${showQuickActions ? 'translate-y-0' : 'translate-y-[calc(100%-80px)]'}`}>\n        <div className=\"bg-black border-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] rounded-t-2xl overflow-hidden\">\n          {/* Toggle Button */}\n          <button\n            onClick={() => setShowQuickActions(!showQuickActions)}\n            className=\"w-full p-4 bg-purple-500 text-white font-black text-lg border-b-4 border-white hover:bg-purple-600 transition-colors flex items-center justify-center space-x-2\"\n          >\n            <span>QUICK ACTIONS</span>\n            <ChevronUp className={`h-6 w-6 transform transition-transform ${showQuickActions ? 'rotate-180' : ''}`} />\n          </button>\n\n          {/* Quick Actions Content */}\n          <div className=\"p-6 space-y-4 w-80\">\n            {/* Add Recipe */}\n            <div className=\"flex items-center space-x-4 p-4 bg-yellow-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:rotate-1\">\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <Plus className=\"h-6 w-6 text-black\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-black text-black text-lg leading-tight\">ADD RECIPE</h3>\n                <p className=\"text-black font-bold text-sm leading-tight\">Create a new recipe</p>\n              </div>\n            </div>\n\n            {/* Start Session */}\n            <div className=\"flex items-center space-x-4 p-4 bg-green-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:-rotate-1\">\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <Clock className=\"h-6 w-6 text-black\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-black text-black text-lg leading-tight\">START SESSION</h3>\n                <p className=\"text-black font-bold text-sm leading-tight\">Begin cooking session</p>\n              </div>\n            </div>\n\n            {/* View Dashboard */}\n            <div className=\"flex items-center space-x-4 p-4 bg-blue-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:rotate-1\">\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <ChefHat className=\"h-6 w-6 text-black\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-black text-black text-lg leading-tight\">DASHBOARD</h3>\n                <p className=\"text-black font-bold text-sm leading-tight\">View cooking status</p>\n              </div>\n            </div>\n\n            {/* Help */}\n            <div\n              onClick={() => setShowGettingStarted(true)}\n              className=\"flex items-center space-x-4 p-4 bg-red-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer\"\n            >\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <Sparkles className=\"h-6 w-6 text-black\" />\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <h3 className=\"font-black text-black text-lg leading-tight\">HELP</h3>\n                <p className=\"text-black font-bold text-sm leading-tight\">Getting started guide</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction DashboardView() {\n  return (\n    <div className=\"space-y-8\">\n      {/* Hero Section */}\n      <div className=\"relative\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-5xl lg:text-6xl font-black text-black mb-3 tracking-tight transform -rotate-2\">\n            DISHSYNC\n          </h1>\n          <div className=\"bg-black border-4 border-white p-4 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto\">\n            <p className=\"text-lg lg:text-xl font-bold text-white leading-tight\">\n              The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency!\n            </p>\n            <div className=\"flex justify-center items-center mt-3 space-x-3\">\n              <Sparkles className=\"h-6 w-6 text-yellow-400 animate-pulse\" />\n              <span className=\"text-yellow-400 font-black text-lg\">COOK LIKE A PRO!</span>\n              <Sparkles className=\"h-6 w-6 text-yellow-400 animate-pulse\" />\n            </div>\n          </div>\n        </div>\n\n        {/* Status Cards Row */}\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"bg-red-500 border-4 border-black rounded-xl p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 hover:scale-105\">\n            <div className=\"flex flex-col items-center justify-center h-full min-h-[80px]\">\n              <div className=\"text-3xl font-black text-white mb-2\">0</div>\n              <div className=\"text-white font-bold text-xs text-center leading-tight\">ACTIVE SESSIONS</div>\n            </div>\n          </div>\n          <div className=\"bg-blue-500 border-4 border-black rounded-xl p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 hover:scale-105\">\n            <div className=\"flex flex-col items-center justify-center h-full min-h-[80px]\">\n              <div className=\"text-3xl font-black text-white mb-2\">0</div>\n              <div className=\"text-white font-bold text-xs text-center leading-tight\">SAVED RECIPES</div>\n            </div>\n          </div>\n          <div className=\"bg-green-500 border-4 border-black rounded-xl p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 hover:scale-105\">\n            <div className=\"flex flex-col items-center justify-center h-full min-h-[80px]\">\n              <div className=\"text-3xl font-black text-white mb-2\">0</div>\n              <div className=\"text-white font-bold text-xs text-center leading-tight\">COMPLETED MEALS</div>\n            </div>\n          </div>\n          <div className=\"bg-purple-500 border-4 border-black rounded-xl p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 hover:scale-105\">\n            <div className=\"flex flex-col items-center justify-center h-full min-h-[80px]\">\n              <div className=\"text-3xl font-black text-white mb-2\">0</div>\n              <div className=\"text-white font-bold text-xs text-center leading-tight\">TIME SAVED</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Features Section */}\n      <div>\n        <h2 className=\"text-3xl font-black text-black mb-6 text-center transform rotate-1\">\n          FEATURES!\n        </h2>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Timer Sync Feature */}\n          <div className=\"bg-gradient-to-br from-blue-400 to-blue-500 border-4 border-black rounded-2xl p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300\">\n            <div className=\"flex items-start space-x-4 mb-6\">\n              <div className=\"bg-white border-4 border-black rounded-xl p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <Clock className=\"h-8 w-8 text-black\" />\n              </div>\n              <div className=\"flex-1\">\n                <h3 className=\"text-2xl font-black text-black mb-2 leading-tight\">TIMER SYNC</h3>\n                <p className=\"text-black font-bold text-sm leading-tight\">Perfect Timing, Every Time</p>\n              </div>\n            </div>\n            <p className=\"text-black font-bold mb-6 text-base leading-relaxed\">\n              Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!\n            </p>\n            <div className=\"flex justify-start\">\n              <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white rounded-lg shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-all duration-300 text-base hover:scale-105\">\n                START SESSION →\n              </button>\n            </div>\n          </div>\n\n          {/* Prep & Cook Feature */}\n          <div className=\"bg-gradient-to-br from-green-400 to-green-500 border-4 border-black rounded-2xl p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300\">\n            <div className=\"flex items-start space-x-4 mb-6\">\n              <div className=\"bg-white border-4 border-black rounded-xl p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <ChefHat className=\"h-8 w-8 text-black\" />\n              </div>\n              <div className=\"flex-1\">\n                <h3 className=\"text-2xl font-black text-black mb-2 leading-tight\">PREP & COOK</h3>\n                <p className=\"text-black font-bold text-sm leading-tight\">Smart Workflow Management</p>\n              </div>\n            </div>\n            <p className=\"text-black font-bold mb-6 text-base leading-relaxed\">\n              Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!\n            </p>\n            <div className=\"flex justify-start\">\n              <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white rounded-lg shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-all duration-300 text-base hover:scale-105\">\n                MANAGE RECIPES →\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Secondary Features */}\n      <div>\n        <h2 className=\"text-2xl font-black text-black mb-4 text-center transform -rotate-1\">\n          BONUS FEATURES!\n        </h2>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"bg-purple-400 border-4 border-black rounded-xl p-5 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300\">\n            <div className=\"flex items-start space-x-3 mb-4\">\n              <div className=\"bg-white border-4 border-black rounded-lg p-2 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <Users className=\"h-6 w-6 text-black\" />\n              </div>\n              <div className=\"flex-1\">\n                <h3 className=\"text-xl font-black text-black leading-tight\">SMART LISTS</h3>\n              </div>\n            </div>\n            <p className=\"text-black font-bold mb-4 text-sm leading-relaxed\">\n              Automatically combine ingredient lists and generate SMART shopping lists!\n            </p>\n            <div className=\"flex justify-start\">\n              <button className=\"bg-black text-white font-black px-4 py-2 border-4 border-white rounded-lg shadow-[3px_3px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-all duration-300 text-sm\">\n                VIEW LISTS →\n              </button>\n            </div>\n          </div>\n\n          <div className=\"bg-yellow-400 border-4 border-black rounded-xl p-5 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300\">\n            <div className=\"flex items-start space-x-3 mb-4\">\n              <div className=\"bg-white border-4 border-black rounded-lg p-2 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] flex-shrink-0\">\n                <Target className=\"h-6 w-6 text-black\" />\n              </div>\n              <div className=\"flex-1\">\n                <h3 className=\"text-xl font-black text-black leading-tight\">MEAL PLANNING</h3>\n              </div>\n            </div>\n            <p className=\"text-black font-bold mb-4 text-sm leading-relaxed\">\n              Plan entire meals with multiple courses and get a MASTER timeline!\n            </p>\n            <div className=\"flex justify-start\">\n              <button className=\"bg-black text-white font-black px-4 py-2 border-4 border-white rounded-lg shadow-[3px_3px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-all duration-300 text-sm\">\n                PLAN MEALS →\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n\nfunction RecipesView() {\n  // Mock data for demonstration - in real app this would come from state/API\n  const hasRecipes = false; // Change to true to see recipes layout\n  const recipeCategories = [\n    { name: \"MAIN DISHES\", count: 0, color: \"bg-red-500\" },\n    { name: \"APPETIZERS\", count: 0, color: \"bg-blue-500\" },\n    { name: \"DESSERTS\", count: 0, color: \"bg-green-500\" },\n    { name: \"SIDES\", count: 0, color: \"bg-purple-500\" }\n  ];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header Section */}\n      <div className=\"relative\">\n        <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6\">\n          <div>\n            <h1 className=\"text-4xl lg:text-5xl font-black text-black transform -rotate-2 mb-2\">\n              MY RECIPES\n            </h1>\n            <p className=\"text-lg font-bold text-black/80\">\n              Organize and manage your culinary creations\n            </p>\n          </div>\n          <button className=\"px-6 py-3 bg-orange-500 text-white font-black text-lg border-4 border-black rounded-xl shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 self-start lg:self-center\">\n            <span className=\"flex items-center gap-2\">ADD RECIPE <Plus className=\"h-5 w-5\" /></span>\n          </button>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n        {recipeCategories.map((category) => (\n          <div key={category.name} className={`${category.color} border-4 border-black rounded-xl p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 cursor-pointer hover:scale-105`}>\n            <div className=\"flex flex-col items-center justify-center h-full min-h-[80px]\">\n              <div className=\"text-3xl font-black text-white mb-2\">{category.count}</div>\n              <div className=\"text-white font-bold text-xs text-center leading-tight\">{category.name}</div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Search and Filter Bar */}\n      <div className=\"bg-black border-4 border-white rounded-2xl p-6 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)]\">\n        <div className=\"flex flex-col md:flex-row gap-4 items-stretch md:items-center\">\n          <div className=\"flex-1\">\n            <div className=\"bg-white border-4 border-black rounded-xl p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] h-full flex items-center\">\n              <input\n                type=\"text\"\n                placeholder=\"SEARCH RECIPES...\"\n                className=\"w-full font-bold text-black placeholder-black/60 bg-transparent outline-none text-base\"\n              />\n            </div>\n          </div>\n          <div className=\"flex gap-3 flex-shrink-0\">\n            <button className=\"px-6 py-4 bg-yellow-400 text-black font-black border-4 border-white rounded-xl shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 text-base\">\n              FILTER\n            </button>\n            <button className=\"px-6 py-4 bg-blue-400 text-black font-black border-4 border-white rounded-xl shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 text-base\">\n              SORT\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      {!hasRecipes ? (\n        /* Empty State */\n        <div className=\"bg-gradient-to-br from-yellow-300 to-yellow-400 border-6 border-black rounded-3xl p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)]\">\n          <div className=\"max-w-2xl mx-auto\">\n            <div className=\"bg-white border-4 border-black rounded-2xl p-8 inline-block shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] mb-8\">\n              <ChefHat className=\"h-20 w-20 text-black\" />\n            </div>\n            <h2 className=\"text-4xl font-black text-black mb-4\">\n              NO RECIPES YET!\n            </h2>\n            <p className=\"text-xl font-bold text-black mb-8 leading-relaxed\">\n              Your recipe collection is waiting to be filled! Start by adding your FIRST recipe\n              and unlock the power of coordinated cooking.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black rounded-xl shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 hover:scale-105\">\n                <span className=\"flex items-center gap-2\">ADD YOUR FIRST RECIPE <Plus className=\"h-6 w-6\" /></span>\n              </button>\n              <button className=\"px-8 py-4 bg-white text-black font-black text-xl border-4 border-black rounded-xl shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 hover:scale-105\">\n                IMPORT RECIPES\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : (\n        /* Recipes Grid - This would show when hasRecipes is true */\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {/* Recipe cards would go here */}\n          <div className=\"bg-white border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n            <div className=\"bg-red-400 border-4 border-black p-4 mb-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <h3 className=\"font-black text-black text-lg\">SAMPLE RECIPE</h3>\n            </div>\n            <p className=\"font-bold text-black mb-4\">A delicious sample recipe description...</p>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-bold text-black/70\">30 mins</span>\n              <button className=\"px-4 py-2 bg-green-500 text-white font-black border-4 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]\">\n                COOK!\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction SessionsView() {\n  // Mock data for demonstration - in real app this would come from state/API\n  const hasSessions = false; // Change to true to see sessions layout\n  const sessionStats = [\n    { name: \"ACTIVE\", count: 0, color: \"bg-green-500\", icon: Clock },\n    { name: \"COMPLETED\", count: 0, color: \"bg-blue-500\", icon: ChefHat },\n    { name: \"PLANNED\", count: 0, color: \"bg-yellow-500\", icon: Target },\n    { name: \"TOTAL TIME SAVED\", count: \"0h\", color: \"bg-purple-500\", icon: Zap }\n  ];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header Section */}\n      <div className=\"relative\">\n        <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6\">\n          <div>\n            <h1 className=\"text-4xl lg:text-5xl font-black text-black transform rotate-2 mb-2\">\n              COOKING SESSIONS\n            </h1>\n            <p className=\"text-lg font-bold text-black/80\">\n              Coordinate multiple dishes with perfect timing\n            </p>\n          </div>\n          <button className=\"px-6 py-3 bg-blue-500 text-white font-black text-lg border-4 border-black rounded-xl shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 self-start lg:self-center\">\n            <span className=\"flex items-center gap-2\">NEW SESSION <Zap className=\"h-5 w-5\" /></span>\n          </button>\n        </div>\n      </div>\n\n      {/* Session Stats */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n        {sessionStats.map((stat) => {\n          const IconComponent = stat.icon;\n          return (\n            <div key={stat.name} className={`${stat.color} border-4 border-black rounded-xl p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 cursor-pointer hover:scale-105`}>\n              <div className=\"flex flex-col items-center justify-center h-full min-h-[100px]\">\n                <IconComponent className=\"h-8 w-8 text-white mb-3\" />\n                <div className=\"text-3xl font-black text-white mb-2\">{stat.count}</div>\n                <div className=\"text-white font-bold text-xs text-center leading-tight\">{stat.name}</div>\n              </div>\n            </div>\n          );\n        })}\n      </div>\n\n      {/* Session Controls */}\n      <div className=\"bg-black border-4 border-white p-6 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform -rotate-1\">\n        <div className=\"flex flex-col md:flex-row gap-6 items-start md:items-center\">\n          <div className=\"flex-1\">\n            <h3 className=\"text-white font-black text-xl mb-2 leading-tight\">QUICK ACTIONS</h3>\n            <p className=\"text-white/80 font-bold text-base leading-relaxed\">Manage your cooking sessions efficiently</p>\n          </div>\n          <div className=\"flex flex-col sm:flex-row gap-3 w-full md:w-auto\">\n            <button className=\"flex items-center justify-center px-6 py-4 bg-green-400 text-black font-black border-4 border-white rounded-xl shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 text-base\">\n              <span className=\"flex items-center gap-2\">ACTIVE TIMERS <Clock className=\"h-5 w-5\" /></span>\n            </button>\n            <button className=\"flex items-center justify-center px-6 py-4 bg-yellow-400 text-black font-black border-4 border-white rounded-xl shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-300 text-base\">\n              <span className=\"flex items-center gap-2\">SCHEDULE <Target className=\"h-5 w-5\" /></span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      {!hasSessions ? (\n        /* Empty State */\n        <div className=\"bg-gradient-to-br from-blue-300 to-blue-400 border-6 border-black rounded-3xl p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)]\">\n          <div className=\"max-w-2xl mx-auto\">\n            <div className=\"bg-white border-4 border-black rounded-2xl p-8 inline-block shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] mb-8\">\n              <Clock className=\"h-20 w-20 text-black\" />\n            </div>\n            <h2 className=\"text-4xl font-black text-black mb-4\">\n              NO SESSIONS YET!\n            </h2>\n            <p className=\"text-xl font-bold text-black mb-8 leading-relaxed\">\n              Create your first cooking session to coordinate MULTIPLE dishes with PERFECT timing!\n              Never serve cold food or burn another dish again.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black rounded-xl shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 hover:scale-105\">\n                <span className=\"flex items-center gap-2\">START YOUR FIRST SESSION <Zap className=\"h-6 w-6\" /></span>\n              </button>\n              <button className=\"px-8 py-4 bg-white text-black font-black text-xl border-4 border-black rounded-xl shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 hover:scale-105\">\n                LEARN HOW IT WORKS\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : (\n        /* Sessions List - This would show when hasSessions is true */\n        <div className=\"space-y-6\">\n          {/* Active Sessions */}\n          <div>\n            <h3 className=\"text-2xl font-black text-black mb-4 transform rotate-1\">ACTIVE SESSIONS</h3>\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <div className=\"bg-green-400 border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n                <div className=\"flex justify-between items-start mb-4\">\n                  <h4 className=\"font-black text-black text-lg\">DINNER PARTY</h4>\n                  <span className=\"bg-white border-2 border-black px-3 py-1 font-black text-sm\">COOKING</span>\n                </div>\n                <p className=\"font-bold text-black mb-4\">3 dishes • 45 mins remaining</p>\n                <div className=\"flex justify-between items-center\">\n                  <div className=\"text-sm font-bold text-black/70\">Started 15 mins ago</div>\n                  <button className=\"px-4 py-2 bg-black text-white font-black border-4 border-white shadow-[3px_3px_0px_0px_rgba(255,255,255,1)]\">\n                    CONTINUE\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n\n          {/* Recent Sessions */}\n          <div>\n            <h3 className=\"text-2xl font-black text-black mb-4 transform -rotate-1\">RECENT SESSIONS</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4\">\n              <div className=\"bg-white border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\">\n                <div className=\"bg-blue-400 border-4 border-black p-3 mb-3 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]\">\n                  <h4 className=\"font-black text-black\">WEEKEND BRUNCH</h4>\n                </div>\n                <p className=\"font-bold text-black mb-3 text-sm\">Completed • 2 dishes • 1h 30m</p>\n                <button className=\"px-3 py-2 bg-purple-500 text-white font-black text-sm border-4 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]\">\n                  REPEAT\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AALA;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwC;IACjF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;;kCAEhB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAoH;;;;;;8DAGlI,8OAAC;oDAAE,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAO1F,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,gIAAgI,EAC1I,cAAc,cACV,gFACA,2IACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,iIAAiI,EAC3I,cAAc,YACV,iFACA,yIACJ;sDACH;;;;;;sDAGD,8OAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,CAAC,gIAAgI,EAC1I,cAAc,aACV,gFACA,2IACJ;sDACH;;;;;;sDAKD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,sBAAsB;oDACrC,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGjB,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,8OAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,8OAAC;;;;;oBAC9B,cAAc,2BAAa,8OAAC;;;;;oBAC5B,cAAc,4BAAc,8OAAC;;;;;;;;;;;YAI/B,oCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;;;;;;;;;;;sDAGZ,8OAAC;4CAAG,WAAU;sDAAiC;;;;;;;;;;;;8CAIjD,8OAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAkD;;;;;;sDAGhE,8OAAC;4CAAG,WAAU;sDAA2C;;;;;;;;;;;;8CAK3D,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;sDAMhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEAA+C;;;;;;;;;;;8DAK9D,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;8EACJ,8OAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS,IAAM,sBAAsB;gCACrC,WAAU;0CAEV,cAAA,8OAAC;oCAAK,WAAU;;wCAA0B;sDAAc,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQrF,8OAAC;gBAAI,WAAW,CAAC,oEAAoE,EAAE,mBAAmB,kBAAkB,iCAAiC;0BAC3J,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;;8CAEV,8OAAC;8CAAK;;;;;;8CACN,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAW,CAAC,uCAAuC,EAAE,mBAAmB,eAAe,IAAI;;;;;;;;;;;;sCAIxG,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAK9D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;8CAK9D,8OAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;;sDAEV,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;8DAC5D,8OAAC;oDAAE,WAAU;8DAA6C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1E;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqF;;;;;;0CAGnG,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAAwD;;;;;;kDAGrE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAyD;;;;;;;;;;;;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAyD;;;;;;;;;;;;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAyD;;;;;;;;;;;;;;;;;0CAG5E,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,8OAAC;4CAAI,WAAU;sDAAyD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOhF,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAqE;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,8OAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;kDAG9D,8OAAC;wCAAE,WAAU;kDAAsD;;;;;;kDAGnE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;sDAAgM;;;;;;;;;;;;;;;;;0CAOtN,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAG,WAAU;kEAAoD;;;;;;kEAClE,8OAAC;wDAAE,WAAU;kEAA6C;;;;;;;;;;;;;;;;;;kDAG9D,8OAAC;wCAAE,WAAU;kDAAsD;;;;;;kDAGnE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;sDAAgM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS1N,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAAsE;;;;;;kCAIpF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;;;;;;;;;;;;kDAGhE,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;sDAA8K;;;;;;;;;;;;;;;;;0CAMpM,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAG,WAAU;8DAA8C;;;;;;;;;;;;;;;;;kDAGhE,8OAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAO,WAAU;sDAA8K;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAW9M;AAEA,SAAS;IACP,2EAA2E;IAC3E,MAAM,aAAa,OAAO,uCAAuC;IACjE,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAe,OAAO;YAAG,OAAO;QAAa;QACrD;YAAE,MAAM;YAAc,OAAO;YAAG,OAAO;QAAc;QACrD;YAAE,MAAM;YAAY,OAAO;YAAG,OAAO;QAAe;QACpD;YAAE,MAAM;YAAS,OAAO;YAAG,OAAO;QAAgB;KACnD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAsE;;;;;;8CAGpF,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAIjD,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAK,WAAU;;oCAA0B;kDAAW,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,yBACrB,8OAAC;wBAAwB,WAAW,GAAG,SAAS,KAAK,CAAC,iMAAiM,CAAC;kCACtP,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CAAuC,SAAS,KAAK;;;;;;8CACpE,8OAAC;oCAAI,WAAU;8CAA0D,SAAS,IAAI;;;;;;;;;;;;uBAHhF,SAAS,IAAI;;;;;;;;;;0BAU3B,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;sCAIhB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAA8N;;;;;;8CAGhP,8OAAC;oCAAO,WAAU;8CAA4N;;;;;;;;;;;;;;;;;;;;;;;YAQnP,uCACC,eAAe,iBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAK,WAAU;;4CAA0B;0DAAsB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAElF,8OAAC;oCAAO,WAAU;8CAAiO;;;;;;;;;;;;;;;;;;;;;;uBAOzP,0DAA0D,iBAC1D;;;;;;;AAkBR;AAEA,SAAS;IACP,2EAA2E;IAC3E,MAAM,cAAc,OAAO,wCAAwC;IACnE,MAAM,eAAe;QACnB;YAAE,MAAM;YAAU,OAAO;YAAG,OAAO;YAAgB,MAAM,oMAAA,CAAA,QAAK;QAAC;QAC/D;YAAE,MAAM;YAAa,OAAO;YAAG,OAAO;YAAe,MAAM,4MAAA,CAAA,UAAO;QAAC;QACnE;YAAE,MAAM;YAAW,OAAO;YAAG,OAAO;YAAiB,MAAM,sMAAA,CAAA,SAAM;QAAC;QAClE;YAAE,MAAM;YAAoB,OAAO;YAAM,OAAO;YAAiB,MAAM,gMAAA,CAAA,MAAG;QAAC;KAC5E;IAED,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqE;;;;;;8CAGnF,8OAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAIjD,8OAAC;4BAAO,WAAU;sCAChB,cAAA,8OAAC;gCAAK,WAAU;;oCAA0B;kDAAY,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC;oBACjB,MAAM,gBAAgB,KAAK,IAAI;oBAC/B,qBACE,8OAAC;wBAAoB,WAAW,GAAG,KAAK,KAAK,CAAC,iMAAiM,CAAC;kCAC9O,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAc,WAAU;;;;;;8CACzB,8OAAC;oCAAI,WAAU;8CAAuC,KAAK,KAAK;;;;;;8CAChE,8OAAC;oCAAI,WAAU;8CAA0D,KAAK,IAAI;;;;;;;;;;;;uBAJ5E,KAAK,IAAI;;;;;gBAQvB;;;;;;0BAIF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;sCAEnE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAK,WAAU;;4CAA0B;0DAAc,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAE3E,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAK,WAAU;;4CAA0B;0DAAS,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAO5E,uCACC,eAAe,iBACf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;;;;;;sCAEnB,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAIjE,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAO,WAAU;8CAChB,cAAA,8OAAC;wCAAK,WAAU;;4CAA0B;0DAAyB,8OAAC,gMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAEpF,8OAAC;oCAAO,WAAU;8CAAiO;;;;;;;;;;;;;;;;;;;;;;uBAOzP,4DAA4D,iBAC5D;;;;;;;AAwCR", "debugId": null}}]}