#!/usr/bin/env node

/**
 * Supabase Setup Script for DishSync
 * 
 * This script helps you set up your Supabase database for DishSync.
 * Run this after creating a new Supabase project.
 * 
 * Usage: node scripts/setup-supabase.js
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(query) {
  return new Promise(resolve => rl.question(query, resolve));
}

async function main() {
  console.log('\n🚀 Welcome to DishSync Supabase Setup!\n');
  
  console.log('This script will help you configure your Supabase database for DishSync.');
  console.log('Make sure you have created a Supabase project at https://supabase.com\n');

  // Get Supabase credentials
  const supabaseUrl = await question('Enter your Supabase Project URL: ');
  const supabaseAnonKey = await question('Enter your Supabase Anon Key: ');
  const supabaseServiceKey = await question('Enter your Supabase Service Role Key (optional): ');

  // Validate inputs
  if (!supabaseUrl || !supabaseAnonKey) {
    console.error('❌ Supabase URL and Anon Key are required!');
    process.exit(1);
  }

  // Create .env.local file
  const envContent = `# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=${supabaseUrl}
NEXT_PUBLIC_SUPABASE_ANON_KEY=${supabaseAnonKey}
${supabaseServiceKey ? `SUPABASE_SERVICE_ROLE_KEY=${supabaseServiceKey}` : '# SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here'}

# Application Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3000
`;

  fs.writeFileSync(path.join(__dirname, '../.env.local'), envContent);
  console.log('✅ Created .env.local file with your Supabase credentials');

  // Instructions for database setup
  console.log('\n📋 Next Steps:');
  console.log('1. Go to your Supabase project dashboard');
  console.log('2. Navigate to the SQL Editor');
  console.log('3. Copy and paste the contents of database/schema.sql');
  console.log('4. Run the SQL to create all tables and policies');
  console.log('5. (Optional) Run database/sample_data.sql to add sample ingredients and recipes');
  console.log('\n🎉 Setup complete! You can now run: npm run dev');

  rl.close();
}

main().catch(console.error);
