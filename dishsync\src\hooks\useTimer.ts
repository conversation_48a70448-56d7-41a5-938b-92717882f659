import { useState, useEffect, useCallback, useRef } from 'react';
import type { Timer } from '@/types';

export interface TimerState {
  timeRemaining: number; // seconds
  isRunning: boolean;
  isPaused: boolean;
  isCompleted: boolean;
  progress: number; // 0-100 percentage
}

export function useTimer(timer: Timer) {
  const [state, setState] = useState<TimerState>({
    timeRemaining: 0,
    isRunning: false,
    isPaused: false,
    isCompleted: false,
    progress: 0,
  });

  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const onCompleteRef = useRef<(() => void) | null>(null);

  // Calculate initial state based on timer
  const calculateTimerState = useCallback((timer: Timer): TimerState => {
    const now = new Date();
    const totalDurationMs = timer.duration_minutes * 60 * 1000;
    
    if (timer.status === 'completed' || timer.status === 'cancelled') {
      return {
        timeRemaining: 0,
        isRunning: false,
        isPaused: false,
        isCompleted: timer.status === 'completed',
        progress: timer.status === 'completed' ? 100 : 0,
      };
    }

    if (timer.status === 'ready' || !timer.start_time) {
      return {
        timeRemaining: timer.duration_minutes * 60,
        isRunning: false,
        isPaused: false,
        isCompleted: false,
        progress: 0,
      };
    }

    const startTime = new Date(timer.start_time);
    const elapsedMs = now.getTime() - startTime.getTime();
    const remainingMs = Math.max(0, totalDurationMs - elapsedMs);
    const remainingSeconds = Math.ceil(remainingMs / 1000);
    
    const progress = Math.min(100, (elapsedMs / totalDurationMs) * 100);
    const isCompleted = remainingMs <= 0;
    
    return {
      timeRemaining: remainingSeconds,
      isRunning: timer.status === 'running' && !isCompleted,
      isPaused: timer.status === 'paused',
      isCompleted,
      progress,
    };
  }, []);

  // Update state when timer changes
  useEffect(() => {
    setState(calculateTimerState(timer));
  }, [timer, calculateTimerState]);

  // Handle timer countdown
  useEffect(() => {
    if (state.isRunning && state.timeRemaining > 0) {
      intervalRef.current = setInterval(() => {
        setState(prevState => {
          const newTimeRemaining = Math.max(0, prevState.timeRemaining - 1);
          const totalSeconds = timer.duration_minutes * 60;
          const progress = ((totalSeconds - newTimeRemaining) / totalSeconds) * 100;
          
          const isCompleted = newTimeRemaining === 0;
          
          if (isCompleted && onCompleteRef.current) {
            onCompleteRef.current();
          }
          
          return {
            ...prevState,
            timeRemaining: newTimeRemaining,
            progress: Math.min(100, progress),
            isCompleted,
            isRunning: !isCompleted,
          };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [state.isRunning, state.timeRemaining, timer.duration_minutes]);

  // Set completion callback
  const setOnComplete = useCallback((callback: () => void) => {
    onCompleteRef.current = callback;
  }, []);

  // Format time remaining as MM:SS
  const formatTimeRemaining = useCallback((seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }, []);

  // Get display color based on timer state
  const getTimerColor = useCallback((): string => {
    if (state.isCompleted) return 'green';
    if (state.isPaused) return 'yellow';
    if (state.timeRemaining <= 60) return 'red'; // Last minute
    if (state.timeRemaining <= 300) return 'orange'; // Last 5 minutes
    return 'blue';
  }, [state]);

  // Play notification sound (browser notification)
  const playNotification = useCallback(() => {
    if (timer.sound_enabled && 'Notification' in window) {
      // Request permission if not already granted
      if (Notification.permission === 'default') {
        Notification.requestPermission();
      }
      
      if (Notification.permission === 'granted') {
        new Notification(`Timer Complete: ${timer.name}`, {
          body: 'Your cooking timer has finished!',
          icon: '/favicon.ico',
          tag: `timer-${timer.id}`,
        });
      }
    }
    
    // Also try to play a sound if available
    try {
      const audio = new Audio('/sounds/timer-complete.mp3');
      audio.play().catch(() => {
        // Fallback to system beep if audio file not available
        console.log('Timer completed:', timer.name);
      });
    } catch (error) {
      console.log('Timer completed:', timer.name);
    }
  }, [timer]);

  // Set up completion notification
  useEffect(() => {
    setOnComplete(() => {
      playNotification();
    });
  }, [setOnComplete, playNotification]);

  return {
    ...state,
    formatTimeRemaining: () => formatTimeRemaining(state.timeRemaining),
    getTimerColor,
    playNotification,
  };
}

// Hook for managing multiple timers
export function useMultipleTimers(timers: Timer[]) {
  const timerStates = timers.map(timer => ({
    timer,
    state: useTimer(timer),
  }));

  const activeTimers = timerStates.filter(({ state }) => state.isRunning);
  const completedTimers = timerStates.filter(({ state }) => state.isCompleted);
  const pausedTimers = timerStates.filter(({ state }) => state.isPaused);

  const hasActiveTimers = activeTimers.length > 0;
  const hasCompletedTimers = completedTimers.length > 0;

  return {
    timerStates,
    activeTimers,
    completedTimers,
    pausedTimers,
    hasActiveTimers,
    hasCompletedTimers,
    totalTimers: timers.length,
  };
}
