import { addMinutes, subMinutes, differenceInMinutes } from 'date-fns';
import type { CookingStep, SessionStep, Recipe } from '@/types';

export interface OptimizationConstraint {
  type: 'equipment' | 'temperature' | 'attention' | 'timing';
  description: string;
  conflictingSteps: string[];
}

export interface TimelineOptimization {
  originalDuration: number;
  optimizedDuration: number;
  timeSaved: number;
  conflicts: OptimizationConstraint[];
  recommendations: string[];
}

/**
 * Optimize the cooking timeline by identifying parallel opportunities and conflicts
 */
export function optimizeTimeline(
  recipes: Recipe[],
  sessionSteps: SessionStep[]
): TimelineOptimization {
  const originalDuration = calculateTimelineDuration(sessionSteps);
  
  // Analyze step conflicts and opportunities
  const conflicts = findStepConflicts(sessionSteps, recipes);
  const parallelOpportunities = findParallelOpportunities(sessionSteps, recipes);
  
  // Apply optimizations
  const optimizedSteps = applyOptimizations(sessionSteps, parallelOpportunities, conflicts);
  const optimizedDuration = calculateTimelineDuration(optimizedSteps);
  
  const timeSaved = originalDuration - optimizedDuration;
  const recommendations = generateRecommendations(conflicts, parallelOpportunities);

  return {
    originalDuration,
    optimizedDuration,
    timeSaved,
    conflicts,
    recommendations,
  };
}

/**
 * Find conflicts between cooking steps
 */
function findStepConflicts(
  sessionSteps: SessionStep[],
  recipes: Recipe[]
): OptimizationConstraint[] {
  const conflicts: OptimizationConstraint[] = [];
  const stepMap = new Map<string, { step: SessionStep; recipe: Recipe; cookingStep: CookingStep }>();

  // Build step lookup map
  sessionSteps.forEach(sessionStep => {
    const recipe = recipes.find(r => r.id === sessionStep.recipe_id);
    const cookingStep = recipe?.steps.find(s => s.id === sessionStep.step_id);
    if (recipe && cookingStep) {
      stepMap.set(sessionStep.id, { step: sessionStep, recipe, cookingStep });
    }
  });

  // Check for equipment conflicts
  const equipmentConflicts = findEquipmentConflicts(Array.from(stepMap.values()));
  conflicts.push(...equipmentConflicts);

  // Check for temperature conflicts (oven usage)
  const temperatureConflicts = findTemperatureConflicts(Array.from(stepMap.values()));
  conflicts.push(...temperatureConflicts);

  // Check for attention conflicts (steps requiring active monitoring)
  const attentionConflicts = findAttentionConflicts(Array.from(stepMap.values()));
  conflicts.push(...attentionConflicts);

  return conflicts;
}

/**
 * Find equipment conflicts between overlapping steps
 */
function findEquipmentConflicts(
  stepData: Array<{ step: SessionStep; recipe: Recipe; cookingStep: CookingStep }>
): OptimizationConstraint[] {
  const conflicts: OptimizationConstraint[] = [];
  const equipmentUsage = new Map<string, Array<{ stepId: string; start: Date; end: Date }>>();

  stepData.forEach(({ step, cookingStep }) => {
    const equipment = cookingStep.equipment || [];
    const startTime = new Date(step.scheduled_start_time);
    const endTime = new Date(step.scheduled_end_time);

    equipment.forEach(eq => {
      if (!equipmentUsage.has(eq)) {
        equipmentUsage.set(eq, []);
      }
      equipmentUsage.get(eq)!.push({
        stepId: step.id,
        start: startTime,
        end: endTime,
      });
    });
  });

  // Check for overlapping usage
  equipmentUsage.forEach((usage, equipment) => {
    const overlapping = findOverlappingTimeSlots(usage);
    if (overlapping.length > 0) {
      conflicts.push({
        type: 'equipment',
        description: `${equipment} is needed by multiple steps at the same time`,
        conflictingSteps: overlapping.map(u => u.stepId),
      });
    }
  });

  return conflicts;
}

/**
 * Find temperature conflicts (oven temperature changes)
 */
function findTemperatureConflicts(
  stepData: Array<{ step: SessionStep; recipe: Recipe; cookingStep: CookingStep }>
): OptimizationConstraint[] {
  const conflicts: OptimizationConstraint[] = [];
  const ovenSteps = stepData.filter(({ cookingStep }) => 
    cookingStep.equipment?.includes('oven') && cookingStep.temperature
  );

  // Group by overlapping time periods
  for (let i = 0; i < ovenSteps.length; i++) {
    for (let j = i + 1; j < ovenSteps.length; j++) {
      const step1 = ovenSteps[i];
      const step2 = ovenSteps[j];
      
      const overlap = getTimeOverlap(
        new Date(step1.step.scheduled_start_time),
        new Date(step1.step.scheduled_end_time),
        new Date(step2.step.scheduled_start_time),
        new Date(step2.step.scheduled_end_time)
      );

      if (overlap > 0 && step1.cookingStep.temperature !== step2.cookingStep.temperature) {
        conflicts.push({
          type: 'temperature',
          description: `Oven temperature conflict: ${step1.cookingStep.temperature}°${step1.cookingStep.temperature_unit} vs ${step2.cookingStep.temperature}°${step2.cookingStep.temperature_unit}`,
          conflictingSteps: [step1.step.id, step2.step.id],
        });
      }
    }
  }

  return conflicts;
}

/**
 * Find attention conflicts (steps requiring active monitoring)
 */
function findAttentionConflicts(
  stepData: Array<{ step: SessionStep; recipe: Recipe; cookingStep: CookingStep }>
): OptimizationConstraint[] {
  const conflicts: OptimizationConstraint[] = [];
  
  // Steps that require active attention
  const attentionSteps = stepData.filter(({ cookingStep }) => {
    const description = cookingStep.description.toLowerCase();
    return description.includes('stir') || 
           description.includes('watch') || 
           description.includes('monitor') ||
           description.includes('flip') ||
           description.includes('adjust');
  });

  // Check for overlapping attention-requiring steps
  for (let i = 0; i < attentionSteps.length; i++) {
    for (let j = i + 1; j < attentionSteps.length; j++) {
      const step1 = attentionSteps[i];
      const step2 = attentionSteps[j];
      
      const overlap = getTimeOverlap(
        new Date(step1.step.scheduled_start_time),
        new Date(step1.step.scheduled_end_time),
        new Date(step2.step.scheduled_start_time),
        new Date(step2.step.scheduled_end_time)
      );

      if (overlap > 5) { // More than 5 minutes overlap
        conflicts.push({
          type: 'attention',
          description: 'Multiple steps require active attention at the same time',
          conflictingSteps: [step1.step.id, step2.step.id],
        });
      }
    }
  }

  return conflicts;
}

/**
 * Find opportunities for parallel execution
 */
function findParallelOpportunities(
  sessionSteps: SessionStep[],
  recipes: Recipe[]
): Array<{ stepIds: string[]; timeSaved: number; description: string }> {
  const opportunities: Array<{ stepIds: string[]; timeSaved: number; description: string }> = [];
  
  // Find prep steps that can be done in parallel
  const prepSteps = sessionSteps.filter(step => {
    const recipe = recipes.find(r => r.id === step.recipe_id);
    const cookingStep = recipe?.steps.find(s => s.id === step.step_id);
    return cookingStep?.is_prep_step;
  });

  if (prepSteps.length > 1) {
    const totalPrepTime = prepSteps.reduce((total, step) => {
      return total + differenceInMinutes(
        new Date(step.scheduled_end_time),
        new Date(step.scheduled_start_time)
      );
    }, 0);

    const maxPrepTime = Math.max(...prepSteps.map(step => 
      differenceInMinutes(
        new Date(step.scheduled_end_time),
        new Date(step.scheduled_start_time)
      )
    ));

    const timeSaved = totalPrepTime - maxPrepTime;
    
    if (timeSaved > 0) {
      opportunities.push({
        stepIds: prepSteps.map(s => s.id),
        timeSaved,
        description: `Prep steps can be done in parallel, saving ${timeSaved} minutes`,
      });
    }
  }

  return opportunities;
}

/**
 * Apply optimizations to the timeline
 */
function applyOptimizations(
  sessionSteps: SessionStep[],
  opportunities: Array<{ stepIds: string[]; timeSaved: number; description: string }>,
  conflicts: OptimizationConstraint[]
): SessionStep[] {
  // For now, return the original steps
  // In a more sophisticated implementation, this would actually reschedule steps
  return sessionSteps;
}

/**
 * Generate optimization recommendations
 */
function generateRecommendations(
  conflicts: OptimizationConstraint[],
  opportunities: Array<{ stepIds: string[]; timeSaved: number; description: string }>
): string[] {
  const recommendations: string[] = [];

  conflicts.forEach(conflict => {
    switch (conflict.type) {
      case 'equipment':
        recommendations.push(`Consider staggering steps that use ${conflict.description.split(' ')[0]} to avoid conflicts`);
        break;
      case 'temperature':
        recommendations.push('Group oven steps by temperature to minimize temperature changes');
        break;
      case 'attention':
        recommendations.push('Schedule attention-requiring steps at different times to avoid multitasking');
        break;
    }
  });

  opportunities.forEach(opportunity => {
    recommendations.push(opportunity.description);
  });

  if (recommendations.length === 0) {
    recommendations.push('Your timeline is well-optimized! No major conflicts detected.');
  }

  return recommendations;
}

/**
 * Helper functions
 */
function calculateTimelineDuration(sessionSteps: SessionStep[]): number {
  if (sessionSteps.length === 0) return 0;
  
  const startTimes = sessionSteps.map(step => new Date(step.scheduled_start_time));
  const endTimes = sessionSteps.map(step => new Date(step.scheduled_end_time));
  
  const earliestStart = new Date(Math.min(...startTimes.map(d => d.getTime())));
  const latestEnd = new Date(Math.max(...endTimes.map(d => d.getTime())));
  
  return differenceInMinutes(latestEnd, earliestStart);
}

function findOverlappingTimeSlots(
  timeSlots: Array<{ stepId: string; start: Date; end: Date }>
): Array<{ stepId: string; start: Date; end: Date }> {
  const overlapping: Array<{ stepId: string; start: Date; end: Date }> = [];
  
  for (let i = 0; i < timeSlots.length; i++) {
    for (let j = i + 1; j < timeSlots.length; j++) {
      const overlap = getTimeOverlap(
        timeSlots[i].start,
        timeSlots[i].end,
        timeSlots[j].start,
        timeSlots[j].end
      );
      
      if (overlap > 0) {
        if (!overlapping.find(slot => slot.stepId === timeSlots[i].stepId)) {
          overlapping.push(timeSlots[i]);
        }
        if (!overlapping.find(slot => slot.stepId === timeSlots[j].stepId)) {
          overlapping.push(timeSlots[j]);
        }
      }
    }
  }
  
  return overlapping;
}

function getTimeOverlap(start1: Date, end1: Date, start2: Date, end2: Date): number {
  const overlapStart = new Date(Math.max(start1.getTime(), start2.getTime()));
  const overlapEnd = new Date(Math.min(end1.getTime(), end2.getTime()));
  
  return Math.max(0, differenceInMinutes(overlapEnd, overlapStart));
}
