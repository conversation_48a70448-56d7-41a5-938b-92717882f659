'use client';

import { useState, useEffect } from 'react';
import {
  Play,
  Pause,
  CheckCircle,
  Clock,
  ChefHat,
  AlertCircle,
  Timer,
  Thermometer,
  Users
} from 'lucide-react';
import Image from 'next/image';
import type { 
  CookingSession, 
  SessionStep, 
  Timer as TimerType, 
  DashboardState,
  CookingTimeline 
} from '@/types';

interface KitchenDashboardProps {
  session: CookingSession;
  timeline: CookingTimeline;
  dashboardState: DashboardState;
  onStartStep: (stepId: string) => void;
  onCompleteStep: (stepId: string) => void;
  onStartTimer: (stepId: string, name: string, duration: number) => void;
  onPauseTimer: (timerId: string) => void;
  onResumeTimer: (timerId: string) => void;
  className?: string;
}

export default function KitchenDashboard({
  session,
  timeline,
  dashboardState,
  onStartStep,
  onCompleteStep,
  onStartTimer,
  onPauseTimer,
  onResumeTimer,
  className = ''
}: KitchenDashboardProps) {
  const [currentTime, setCurrentTime] = useState(new Date());

  // Update current time every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  const getStepStatus = (step: SessionStep): 'upcoming' | 'ready' | 'active' | 'completed' | 'overdue' => {
    const now = currentTime;
    const startTime = new Date(step.scheduled_start_time);
    const endTime = new Date(step.scheduled_end_time);

    if (step.status === 'completed') return 'completed';
    if (step.status === 'in_progress') return 'active';
    
    if (now < startTime) return 'upcoming';
    if (now >= startTime && now <= endTime) return 'ready';
    if (now > endTime) return 'overdue';
    
    return 'upcoming';
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'upcoming': return 'bg-gray-100 border-gray-300 text-gray-700';
      case 'ready': return 'bg-yellow-100 border-yellow-300 text-yellow-800';
      case 'active': return 'bg-blue-100 border-blue-300 text-blue-800';
      case 'completed': return 'bg-green-100 border-green-300 text-green-800';
      case 'overdue': return 'bg-red-100 border-red-300 text-red-800';
      default: return 'bg-gray-100 border-gray-300 text-gray-700';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'upcoming': return <Clock className="h-4 w-4" />;
      case 'ready': return <Play className="h-4 w-4" />;
      case 'active': return <ChefHat className="h-4 w-4" />;
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'overdue': return <AlertCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  // Group steps by recipe
  const stepsByRecipe = timeline.steps.reduce((groups, step) => {
    const recipeId = step.recipe_id;
    if (!groups[recipeId]) {
      groups[recipeId] = [];
    }
    groups[recipeId].push(step);
    return groups;
  }, {} as Record<string, SessionStep[]>);

  return (
    <div className={`bg-yellow-300 border-8 border-black shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] ${className}`}>
      {/* Header */}
      <div className="p-8 border-b-8 border-black bg-red-500">
        <div className="flex items-center justify-between mb-6">
          <h1 className="text-4xl font-black text-white flex items-center transform -rotate-2">
            <div className="bg-white border-4 border-black p-3 mr-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)]">
              <Image
                src="/assets/chef_timer.png"
                alt="Chef Timer"
                width={48}
                height={48}
              />
            </div>
            KITCHEN DASHBOARD!
          </h1>
          <div className="bg-white border-4 border-black p-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-2">
            <div className="text-lg font-black text-black">
              SESSION: {session.name}
            </div>
            <div className="text-lg font-black text-black">
              STATUS: <span className="uppercase text-red-600">{session.status}</span>
            </div>
          </div>
        </div>

        {/* Session Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-blue-400 border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="flex items-center">
              <div className="bg-white border-4 border-black p-2 mr-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                <Timer className="h-6 w-6 text-black" />
              </div>
              <div>
                <p className="text-lg font-black text-black">ACTIVE TIMERS</p>
                <p className="text-3xl font-black text-white">
                  {dashboardState.active_timers.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-yellow-400 border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="flex items-center">
              <div className="bg-white border-4 border-black p-2 mr-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                <Clock className="h-6 w-6 text-black" />
              </div>
              <div>
                <p className="text-lg font-black text-black">NEXT STEPS</p>
                <p className="text-3xl font-black text-white">
                  {dashboardState.next_steps.length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-green-400 border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="flex items-center">
              <div className="bg-white border-4 border-black p-2 mr-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                <CheckCircle className="h-6 w-6 text-black" />
              </div>
              <div>
                <p className="text-lg font-black text-black">COMPLETED</p>
                <p className="text-3xl font-black text-white">
                  {timeline.steps.filter(s => s.status === 'completed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-purple-400 border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="flex items-center">
              <div className="bg-white border-4 border-black p-2 mr-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                <Users className="h-6 w-6 text-black" />
              </div>
              <div>
                <p className="text-lg font-black text-black">RECIPES</p>
                <p className="text-3xl font-black text-white">
                  {Object.keys(stepsByRecipe).length}
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Timers */}
      {dashboardState.active_timers.length > 0 && (
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Timer className="mr-2 h-5 w-5 text-blue-600" />
            Active Timers
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {dashboardState.active_timers.map(timer => (
              <TimerCard
                key={timer.id}
                timer={timer}
                onPause={() => onPauseTimer(timer.id)}
                onResume={() => onResumeTimer(timer.id)}
              />
            ))}
          </div>
        </div>
      )}

      {/* Current Step */}
      {dashboardState.current_step && (
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <ChefHat className="mr-2 h-5 w-5 text-green-600" />
            Current Step
          </h2>
          <CurrentStepCard
            step={dashboardState.current_step}
            onComplete={() => onCompleteStep(dashboardState.current_step!.id)}
            onStartTimer={onStartTimer}
          />
        </div>
      )}

      {/* Recipe Progress */}
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">Recipe Progress</h2>
        <div className="space-y-6">
          {Object.entries(stepsByRecipe).map(([recipeId, steps]) => {
            const recipe = session.recipes?.find(r => r.id === recipeId);
            return (
              <RecipeProgressCard
                key={recipeId}
                recipe={recipe}
                steps={steps}
                currentTime={currentTime}
                onStartStep={onStartStep}
                onCompleteStep={onCompleteStep}
                onStartTimer={onStartTimer}
                getStepStatus={getStepStatus}
                getStatusColor={getStatusColor}
                getStatusIcon={getStatusIcon}
              />
            );
          })}
        </div>
      </div>
    </div>
  );
}

interface TimerCardProps {
  timer: TimerType;
  onPause: () => void;
  onResume: () => void;
}

function TimerCard({ timer, onPause, onResume }: TimerCardProps) {
  const [timeRemaining, setTimeRemaining] = useState(0);

  useEffect(() => {
    if (timer.status === 'running' && timer.end_time) {
      const interval = setInterval(() => {
        const now = new Date();
        const end = new Date(timer.end_time!);
        const remaining = Math.max(0, Math.ceil((end.getTime() - now.getTime()) / 1000));
        setTimeRemaining(remaining);
      }, 1000);

      return () => clearInterval(interval);
    }
  }, [timer]);

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getTimerColor = (): string => {
    if (timer.status === 'paused') return 'border-yellow-300 bg-yellow-50';
    if (timeRemaining <= 60) return 'border-red-300 bg-red-50';
    if (timeRemaining <= 300) return 'border-orange-300 bg-orange-50';
    return 'border-blue-300 bg-blue-50';
  };

  return (
    <div className={`border-2 rounded-lg p-4 ${getTimerColor()}`}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="font-medium text-gray-900 truncate">{timer.name}</h3>
        <div className="flex space-x-1">
          {timer.status === 'running' ? (
            <button
              onClick={onPause}
              className="p-1 text-gray-600 hover:text-gray-800"
            >
              <Pause className="h-4 w-4" />
            </button>
          ) : (
            <button
              onClick={onResume}
              className="p-1 text-gray-600 hover:text-gray-800"
            >
              <Play className="h-4 w-4" />
            </button>
          )}
        </div>
      </div>
      <div className="text-2xl font-bold text-center">
        {formatTime(timeRemaining)}
      </div>
      <div className="text-xs text-gray-600 text-center mt-1">
        {timer.status === 'paused' ? 'Paused' : 'Running'}
      </div>
    </div>
  );
}

interface RecipeProgressCardProps {
  recipe?: any; // Recipe type
  steps: SessionStep[];
  currentTime: Date;
  onStartStep: (stepId: string) => void;
  onCompleteStep: (stepId: string) => void;
  onStartTimer: (stepId: string, name: string, duration: number) => void;
  getStepStatus: (step: SessionStep) => string;
  getStatusColor: (status: string) => string;
  getStatusIcon: (status: string) => React.ReactNode;
}

function RecipeProgressCard({
  recipe,
  steps,
  currentTime,
  onStartStep,
  onCompleteStep,
  onStartTimer,
  getStepStatus,
  getStatusColor,
  getStatusIcon,
}: RecipeProgressCardProps) {
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const totalSteps = steps.length;
  const progressPercentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

  const sortedSteps = steps.sort((a, b) =>
    new Date(a.scheduled_start_time).getTime() - new Date(b.scheduled_start_time).getTime()
  );

  return (
    <div className="border border-gray-200 rounded-lg p-4">
      <div className="flex items-center justify-between mb-4">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {recipe?.name || 'Unknown Recipe'}
          </h3>
          <p className="text-sm text-gray-600">
            {completedSteps} of {totalSteps} steps completed ({progressPercentage}%)
          </p>
        </div>

        {/* Progress Bar */}
        <div className="w-32">
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-green-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>
      </div>

      {/* Steps Timeline */}
      <div className="space-y-2">
        {sortedSteps.map((step, index) => {
          const status = getStepStatus(step);
          const startTime = new Date(step.scheduled_start_time);
          const endTime = new Date(step.scheduled_end_time);

          return (
            <div
              key={step.id}
              className={`border rounded-lg p-3 ${getStatusColor(status)}`}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="flex items-center justify-center w-6 h-6 rounded-full bg-white">
                    {getStatusIcon(status)}
                  </div>

                  <div className="flex-1">
                    <h4 className="font-medium">
                      {step.step?.title || `Step ${index + 1}`}
                    </h4>
                    <p className="text-sm opacity-90">
                      {step.step?.description || 'No description'}
                    </p>
                    <div className="text-xs opacity-75 mt-1">
                      {startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })} -
                      {endTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      {step.step?.duration_minutes && ` (${step.step.duration_minutes}m)`}
                    </div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  {status === 'ready' && step.status !== 'in_progress' && (
                    <button
                      onClick={() => onStartStep(step.id)}
                      className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
                    >
                      Start
                    </button>
                  )}

                  {status === 'active' && (
                    <>
                      {step.step?.duration_minutes && (
                        <button
                          onClick={() => onStartTimer(
                            step.id,
                            step.step?.title || 'Timer',
                            step.step.duration_minutes
                          )}
                          className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
                        >
                          Timer
                        </button>
                      )}
                      <button
                        onClick={() => onCompleteStep(step.id)}
                        className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
                      >
                        Done
                      </button>
                    </>
                  )}

                  {status === 'overdue' && step.status !== 'completed' && (
                    <button
                      onClick={() => onCompleteStep(step.id)}
                      className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                    >
                      Mark Done
                    </button>
                  )}
                </div>
              </div>

              {/* Additional step info */}
              {(step.step?.temperature || step.step?.equipment) && (
                <div className="mt-2 pt-2 border-t border-current border-opacity-20">
                  <div className="flex space-x-4 text-xs">
                    {step.step.temperature && (
                      <span className="flex items-center">
                        <Thermometer className="h-3 w-3 mr-1" />
                        {step.step.temperature}°{step.step.temperature_unit || 'F'}
                      </span>
                    )}
                    {step.step.equipment && step.step.equipment.length > 0 && (
                      <span>
                        Equipment: {step.step.equipment.join(', ')}
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
}

interface CurrentStepCardProps {
  step: SessionStep;
  onComplete: () => void;
  onStartTimer: (stepId: string, name: string, duration: number) => void;
}

function CurrentStepCard({ step, onComplete, onStartTimer }: CurrentStepCardProps) {
  return (
    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-green-900 mb-2">
            {step.step?.title || 'Current Step'}
          </h3>
          <p className="text-green-800 mb-4">
            {step.step?.description || 'No description available'}
          </p>
          
          {step.step?.temperature && (
            <div className="flex items-center text-sm text-green-700 mb-2">
              <Thermometer className="h-4 w-4 mr-1" />
              {step.step.temperature}°{step.step.temperature_unit || 'F'}
            </div>
          )}

          {step.step?.equipment && step.step.equipment.length > 0 && (
            <div className="text-sm text-green-700 mb-4">
              <strong>Equipment:</strong> {step.step.equipment.join(', ')}
            </div>
          )}
        </div>

        <div className="flex flex-col space-y-2 ml-4">
          {step.step?.duration_minutes && (
            <button
              onClick={() => onStartTimer(
                step.id, 
                step.step?.title || 'Step Timer', 
                step.step.duration_minutes
              )}
              className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm"
            >
              Start Timer ({step.step.duration_minutes}m)
            </button>
          )}
          
          <button
            onClick={onComplete}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Complete Step
          </button>
        </div>
      </div>
    </div>
  );
}
