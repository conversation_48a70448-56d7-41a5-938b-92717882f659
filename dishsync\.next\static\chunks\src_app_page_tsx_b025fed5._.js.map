{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/test/dishsync/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n// import GettingStartedModal from '@/components/GettingStartedModal';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');\n  const [showGettingStarted, setShowGettingStarted] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-yellow-300\">\n      {/* Header */}\n      <header className=\"bg-black border-b-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)]\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-20\">\n            <div className=\"flex items-center space-x-4\">\n              <Image\n                src=\"/assets/logo.png\"\n                alt=\"DishSync Logo\"\n                width={48}\n                height={48}\n                className=\"rounded-lg border-2 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\"\n              />\n              <h1 className=\"text-3xl font-black text-white tracking-tight\">DISHSYNC</h1>\n            </div>\n            <nav className=\"flex space-x-4\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${\n                  activeTab === 'dashboard'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'\n                }`}\n              >\n                DASHBOARD\n              </button>\n              <button\n                onClick={() => setActiveTab('recipes')}\n                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${\n                  activeTab === 'recipes'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'\n                }`}\n              >\n                RECIPES\n              </button>\n              <button\n                onClick={() => setActiveTab('sessions')}\n                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${\n                  activeTab === 'sessions'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'\n                }`}\n              >\n                SESSIONS\n              </button>\n              <button\n                onClick={() => setShowGettingStarted(true)}\n                className=\"px-6 py-3 font-black text-lg border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none hover:bg-green-600\"\n              >\n                HELP\n              </button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {activeTab === 'dashboard' && <DashboardView />}\n        {activeTab === 'recipes' && <RecipesView />}\n        {activeTab === 'sessions' && <SessionsView />}\n      </main>\n\n      {/* Getting Started Modal */}\n      {/* <GettingStartedModal\n        isOpen={showGettingStarted}\n        onClose={() => setShowGettingStarted(false)}\n      /> */}\n    </div>\n  );\n}\n\nfunction DashboardView() {\n  return (\n    <div className=\"space-y-12\">\n      {/* Welcome Section */}\n      <div className=\"text-center\">\n        <h2 className=\"text-6xl font-black text-black mb-6 tracking-tight transform -rotate-1\">\n          WELCOME TO DISHSYNC!\n        </h2>\n        <div className=\"bg-black border-4 border-white p-8 shadow-[12px_12px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto\">\n          <p className=\"text-2xl font-bold text-white leading-relaxed\">\n            The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency.\n            Never worry about timing multiple dishes again!\n          </p>\n          <div className=\"flex justify-center mt-6\">\n            <Sparkles className=\"h-8 w-8 text-yellow-400 animate-pulse\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Feature Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        <div className=\"bg-blue-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <Clock className=\"h-8 w-8 text-black\" />\n            </div>\n            <h3 className=\"text-2xl font-black text-black\">TIMER SYNC</h3>\n          </div>\n          <p className=\"text-black font-bold mb-6 text-lg leading-tight\">\n            Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!\n          </p>\n          <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors\">\n            START SESSION →\n          </button>\n        </div>\n\n        <div className=\"bg-green-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <ChefHat className=\"h-8 w-8 text-black\" />\n            </div>\n            <h3 className=\"text-2xl font-black text-black\">PREP & COOK</h3>\n          </div>\n          <p className=\"text-black font-bold mb-6 text-lg leading-tight\">\n            Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!\n          </p>\n          <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors\">\n            MANAGE RECIPES →\n          </button>\n        </div>\n\n        <div className=\"bg-purple-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <Users className=\"h-8 w-8 text-black\" />\n            </div>\n            <h3 className=\"text-2xl font-black text-black\">SMART LISTS</h3>\n          </div>\n          <p className=\"text-black font-bold mb-6 text-lg leading-tight\">\n            Automatically combine ingredient lists and generate SMART shopping lists!\n          </p>\n          <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors\">\n            VIEW LISTS →\n          </button>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-red-500 border-4 border-black p-8 shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1\">\n        <h3 className=\"text-3xl font-black text-white mb-8 text-center transform rotate-1\">QUICK ACTIONS!</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <button className=\"flex items-center justify-center px-6 py-4 bg-yellow-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3\">\n            <Plus className=\"mr-2 h-5 w-5\" />\n            ADD RECIPE\n          </button>\n          <button className=\"flex items-center justify-center px-6 py-4 bg-blue-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3\">\n            <Clock className=\"mr-2 h-5 w-5\" />\n            NEW SESSION\n          </button>\n          <button className=\"flex items-center justify-center px-6 py-4 bg-green-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3\">\n            <ChefHat className=\"mr-2 h-5 w-5\" />\n            BROWSE RECIPES\n          </button>\n          <button className=\"flex items-center justify-center px-6 py-4 bg-purple-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3\">\n            <Target className=\"mr-2 h-5 w-5\" />\n            DASHBOARD\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction RecipesView() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-5xl font-black text-black transform -rotate-2\">MY RECIPES</h2>\n        <button className=\"px-8 py-4 bg-orange-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform rotate-2\">\n          <Plus className=\"mr-2 h-6 w-6\" />\n          ADD RECIPE!\n        </button>\n      </div>\n\n      <div className=\"bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1\">\n        <div className=\"bg-yellow-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-6\">\n          <ChefHat className=\"h-16 w-16 text-black\" />\n        </div>\n        <h3 className=\"text-3xl font-black text-black mb-4\">NO RECIPES YET!</h3>\n        <p className=\"text-xl font-bold text-black mb-8 max-w-md mx-auto\">\n          Start by adding your FIRST recipe to begin coordinating your cooking like a PRO!\n        </p>\n        <button className=\"px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <Plus className=\"mr-2 h-6 w-6\" />\n          ADD YOUR FIRST RECIPE!\n        </button>\n      </div>\n    </div>\n  );\n}\n\nfunction SessionsView() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-5xl font-black text-black transform rotate-2\">COOKING SESSIONS</h2>\n        <button className=\"px-8 py-4 bg-blue-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform -rotate-2\">\n          <Zap className=\"mr-2 h-6 w-6\" />\n          NEW SESSION!\n        </button>\n      </div>\n\n      <div className=\"bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1\">\n        <div className=\"bg-blue-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-3 mb-6\">\n          <Clock className=\"h-16 w-16 text-black\" />\n        </div>\n        <h3 className=\"text-3xl font-black text-black mb-4\">NO SESSIONS YET!</h3>\n        <p className=\"text-xl font-bold text-black mb-8 max-w-md mx-auto\">\n          Create a cooking session to coordinate MULTIPLE dishes with PERFECT timing!\n        </p>\n        <button className=\"px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <Zap className=\"mr-2 h-6 w-6\" />\n          START YOUR FIRST SESSION!\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AALA;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IACjF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,6LAAC;wCAAG,WAAU;kDAAgD;;;;;;;;;;;;0CAEhE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,yIAIX,OAHC,cAAc,cACV,uEACA;kDAEP;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,yIAIX,OAHC,cAAc,YACV,uEACA;kDAEP;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,AAAC,yIAIX,OAHC,cAAc,aACV,uEACA;kDAEP;;;;;;kDAGD,6LAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,6LAAC;;;;;oBAC9B,cAAc,2BAAa,6LAAC;;;;;oBAC5B,cAAc,4BAAc,6LAAC;;;;;;;;;;;;;;;;;AAUtC;GA5EwB;KAAA;AA8ExB,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyE;;;;;;kCAGvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAI7D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;0CAEjD,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAG/D,6LAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;kCAKrK,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;0CAEjD,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAG/D,6LAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;kCAKrK,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,6LAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;0CAEjD,6LAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAG/D,6LAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;;;;;;;0BAOvK,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqE;;;;;;kCACnF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,qMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,+MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGtC,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;MA3FS;AA6FT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqD;;;;;;kCACnE,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAGlE,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,qMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM3C;MA1BS;AA4BT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAClE,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAGlE,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM1C;MA1BS", "debugId": null}}]}