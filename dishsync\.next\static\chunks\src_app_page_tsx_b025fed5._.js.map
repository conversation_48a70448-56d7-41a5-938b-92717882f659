{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/test/dishsync/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles, X, ChevronUp } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\n\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');\n  const [showGettingStarted, setShowGettingStarted] = useState(false);\n  const [showQuickActions, setShowQuickActions] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-yellow-300\">\n      {/* Header */}\n      <header className=\"bg-black border-b-8 border-yellow-400 relative overflow-hidden\">\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-red-500 via-yellow-400 to-blue-500\"></div>\n        </div>\n\n        <div className=\"relative max-w-7xl mx-auto px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-6\">\n            {/* Logo Section */}\n            <div className=\"flex items-center space-x-6\">\n              <div className=\"relative\">\n                <div className=\"bg-yellow-400 border-4 border-white p-3 shadow-[6px_6px_0px_0px_rgba(255,255,255,1)] transform rotate-3 hover:rotate-6 transition-transform\">\n                  <Image\n                    src=\"/assets/logo.png\"\n                    alt=\"DishSync Logo\"\n                    width={56}\n                    height={56}\n                    className=\"rounded-lg\"\n                  />\n                </div>\n              </div>\n              <div className=\"flex flex-col\">\n                <h1 className=\"text-4xl lg:text-5xl font-black text-white tracking-tight transform -rotate-1 hover:rotate-0 transition-transform\">\n                  DISHSYNC\n                </h1>\n                <p className=\"text-yellow-400 font-bold text-sm lg:text-base transform rotate-1 mt-1\">\n                  COOK LIKE A PRO!\n                </p>\n              </div>\n            </div>\n\n            {/* Navigation */}\n            <nav className=\"flex items-center space-x-3\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${\n                  activeTab === 'dashboard'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'\n                }`}\n              >\n                DASHBOARD\n              </button>\n              <button\n                onClick={() => setActiveTab('recipes')}\n                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:-rotate-1 ${\n                  activeTab === 'recipes'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] -rotate-1'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-blue-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'\n                }`}\n              >\n                RECIPES\n              </button>\n              <button\n                onClick={() => setActiveTab('sessions')}\n                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${\n                  activeTab === 'sessions'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-purple-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'\n                }`}\n              >\n                SESSIONS\n              </button>\n\n              {/* Help Button - Special Styling */}\n              <div className=\"relative ml-2\">\n                <button\n                  onClick={() => setShowGettingStarted(true)}\n                  className=\"px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:scale-110 hover:rotate-3 hover:bg-green-400 hover:shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] relative overflow-hidden\"\n                >\n                  <span className=\"relative z-10\">HELP</span>\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 opacity-0 hover:opacity-100 transition-opacity\"></div>\n                </button>\n                {/* Pulsing indicator */}\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 border-2 border-white rounded-full animate-pulse\"></div>\n              </div>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {activeTab === 'dashboard' && <DashboardView />}\n        {activeTab === 'recipes' && <RecipesView />}\n        {activeTab === 'sessions' && <SessionsView />}\n      </main>\n\n      {/* Getting Started Modal */}\n      {showGettingStarted && (\n        <div className=\"fixed inset-0 backdrop-blur-md bg-black/20 flex items-center justify-center z-50 p-4\">\n          <div className=\"bg-yellow-400 border-8 border-black shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] w-full max-w-5xl h-[85vh] flex flex-col transform rotate-1\">\n            {/* Header */}\n            <div className=\"flex justify-between items-center p-6 border-b-4 border-black flex-shrink-0\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"bg-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n                  <Image\n                    src=\"/assets/logo.png\"\n                    alt=\"DishSync Logo\"\n                    width={32}\n                    height={32}\n                  />\n                </div>\n                <h2 className=\"text-2xl font-black text-black\">\n                  GETTING STARTED GUIDE\n                </h2>\n              </div>\n              <button\n                onClick={() => setShowGettingStarted(false)}\n                className=\"bg-red-500 text-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n              >\n                <X className=\"h-6 w-6\" />\n              </button>\n            </div>\n\n            {/* Content - Flex grow to fill available space */}\n            <div className=\"flex-1 flex flex-col p-6\">\n              <div className=\"text-center mb-6\">\n                <h1 className=\"text-4xl lg:text-5xl font-black text-black mb-3 transform -rotate-2\">\n                  WELCOME TO DISHSYNC!\n                </h1>\n                <h2 className=\"text-xl lg:text-2xl font-bold text-black transform rotate-1\">\n                  Your Ultimate Cooking Companion\n                </h2>\n              </div>\n\n              <div className=\"flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 items-center\">\n                {/* Image */}\n                <div className=\"flex justify-center\">\n                  <div className=\"bg-white border-4 border-black p-4 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3\">\n                    <Image\n                      src=\"/assets/chef.png\"\n                      alt=\"Chef illustration\"\n                      width={280}\n                      height={280}\n                      className=\"rounded-lg\"\n                    />\n                  </div>\n                </div>\n\n                {/* Content */}\n                <div className=\"space-y-4\">\n                  <div className=\"bg-white border-4 border-black p-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-1\">\n                    <p className=\"text-lg font-bold text-black leading-relaxed\">\n                      DishSync is the REVOLUTIONARY multi-dish cooking assistant that will transform your kitchen experience! Say goodbye to burnt food and timing disasters!\n                    </p>\n                  </div>\n\n                  <div className=\"bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                    <h3 className=\"text-white font-black text-lg mb-2\">HOW TO GET STARTED:</h3>\n                    <ul className=\"text-white font-bold space-y-1\">\n                      <li>1. Add your favorite RECIPES</li>\n                      <li>2. Create a COOKING SESSION</li>\n                      <li>3. Follow the PREP MODE</li>\n                      <li>4. Switch to COOK MODE</li>\n                      <li>5. Enjoy PERFECT results!</li>\n                    </ul>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Footer */}\n            <div className=\"flex justify-center p-6 border-t-4 border-black flex-shrink-0\">\n              <button\n                onClick={() => setShowGettingStarted(false)}\n                className=\"px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n              >\n                START COOKING! 🍳\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Quick Actions - Collapsible from bottom */}\n      <div className={`fixed bottom-0 right-6 z-40 transition-all duration-300 ease-in-out ${showQuickActions ? 'translate-y-0' : 'translate-y-[calc(100%-80px)]'}`}>\n        <div className=\"bg-black border-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] rounded-t-2xl overflow-hidden\">\n          {/* Toggle Button */}\n          <button\n            onClick={() => setShowQuickActions(!showQuickActions)}\n            className=\"w-full p-4 bg-purple-500 text-white font-black text-lg border-b-4 border-white hover:bg-purple-600 transition-colors flex items-center justify-center space-x-2\"\n          >\n            <Zap className=\"h-6 w-6\" />\n            <span>QUICK ACTIONS</span>\n            <ChevronUp className={`h-6 w-6 transform transition-transform ${showQuickActions ? 'rotate-180' : ''}`} />\n          </button>\n\n          {/* Quick Actions Content */}\n          <div className=\"p-6 space-y-4 w-80\">\n            {/* Add Recipe */}\n            <div className=\"flex items-center space-x-4 p-4 bg-yellow-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:rotate-1\">\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n                <Plus className=\"h-6 w-6 text-black\" />\n              </div>\n              <div>\n                <h3 className=\"font-black text-black text-lg\">ADD RECIPE</h3>\n                <p className=\"text-black font-bold text-sm\">Create a new recipe</p>\n              </div>\n            </div>\n\n            {/* Start Session */}\n            <div className=\"flex items-center space-x-4 p-4 bg-green-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:-rotate-1\">\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n                <Clock className=\"h-6 w-6 text-black\" />\n              </div>\n              <div>\n                <h3 className=\"font-black text-black text-lg\">START SESSION</h3>\n                <p className=\"text-black font-bold text-sm\">Begin cooking session</p>\n              </div>\n            </div>\n\n            {/* View Dashboard */}\n            <div className=\"flex items-center space-x-4 p-4 bg-blue-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:rotate-1\">\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n                <ChefHat className=\"h-6 w-6 text-black\" />\n              </div>\n              <div>\n                <h3 className=\"font-black text-black text-lg\">DASHBOARD</h3>\n                <p className=\"text-black font-bold text-sm\">View cooking status</p>\n              </div>\n            </div>\n\n            {/* Help */}\n            <div\n              onClick={() => setShowGettingStarted(true)}\n              className=\"flex items-center space-x-4 p-4 bg-red-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:-rotate-1\"\n            >\n              <div className=\"w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n                <Sparkles className=\"h-6 w-6 text-black\" />\n              </div>\n              <div>\n                <h3 className=\"font-black text-black text-lg\">HELP</h3>\n                <p className=\"text-black font-bold text-sm\">Getting started guide</p>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction DashboardView() {\n  return (\n    <div className=\"space-y-8\">\n      {/* Hero Section */}\n      <div className=\"relative\">\n        <div className=\"text-center mb-8\">\n          <h1 className=\"text-5xl lg:text-6xl font-black text-black mb-3 tracking-tight transform -rotate-2\">\n            DISHSYNC\n          </h1>\n          <div className=\"bg-black border-4 border-white p-4 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto\">\n            <p className=\"text-lg lg:text-xl font-bold text-white leading-tight\">\n              The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency!\n            </p>\n            <div className=\"flex justify-center items-center mt-3 space-x-3\">\n              <Sparkles className=\"h-6 w-6 text-yellow-400 animate-pulse\" />\n              <span className=\"text-yellow-400 font-black text-lg\">COOK LIKE A PRO!</span>\n              <Sparkles className=\"h-6 w-6 text-yellow-400 animate-pulse\" />\n            </div>\n          </div>\n        </div>\n\n        {/* Status Cards Row */}\n        <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n          <div className=\"bg-red-500 border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform rotate-1 hover:rotate-3 transition-transform\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-black text-white mb-1\">0</div>\n              <div className=\"text-white font-bold text-sm\">ACTIVE SESSIONS</div>\n            </div>\n          </div>\n          <div className=\"bg-blue-500 border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform -rotate-1 hover:-rotate-3 transition-transform\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-black text-white mb-1\">0</div>\n              <div className=\"text-white font-bold text-sm\">SAVED RECIPES</div>\n            </div>\n          </div>\n          <div className=\"bg-green-500 border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform rotate-1 hover:rotate-3 transition-transform\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-black text-white mb-1\">0</div>\n              <div className=\"text-white font-bold text-sm\">COMPLETED MEALS</div>\n            </div>\n          </div>\n          <div className=\"bg-purple-500 border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform -rotate-1 hover:-rotate-3 transition-transform\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-black text-white mb-1\">0</div>\n              <div className=\"text-white font-bold text-sm\">TIME SAVED</div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Features Section */}\n      <div>\n        <h2 className=\"text-3xl font-black text-black mb-6 text-center transform rotate-1\">\n          POWERFUL FEATURES!\n        </h2>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {/* Timer Sync Feature */}\n          <div className=\"bg-gradient-to-br from-blue-400 to-blue-500 border-6 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n            <div className=\"flex items-center space-x-4 mb-4\">\n              <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform rotate-12\">\n                <Clock className=\"h-8 w-8 text-black\" />\n              </div>\n              <div>\n                <h3 className=\"text-2xl font-black text-black mb-1\">TIMER SYNC</h3>\n                <p className=\"text-black font-bold text-sm\">Perfect Timing, Every Time</p>\n              </div>\n            </div>\n            <p className=\"text-black font-bold mb-4 text-base leading-tight\">\n              Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!\n            </p>\n            <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-base transform hover:scale-105\">\n              START SESSION →\n            </button>\n          </div>\n\n          {/* Prep & Cook Feature */}\n          <div className=\"bg-gradient-to-br from-green-400 to-green-500 border-6 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n            <div className=\"flex items-center space-x-4 mb-4\">\n              <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform -rotate-12\">\n                <ChefHat className=\"h-8 w-8 text-black\" />\n              </div>\n              <div>\n                <h3 className=\"text-2xl font-black text-black mb-1\">PREP & COOK</h3>\n                <p className=\"text-black font-bold text-sm\">Smart Workflow Management</p>\n              </div>\n            </div>\n            <p className=\"text-black font-bold mb-4 text-base leading-tight\">\n              Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!\n            </p>\n            <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-base transform hover:scale-105\">\n              MANAGE RECIPES →\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Secondary Features */}\n      <div>\n        <h2 className=\"text-2xl font-black text-black mb-4 text-center transform -rotate-1\">\n          BONUS FEATURES!\n        </h2>\n\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n          <div className=\"bg-purple-400 border-4 border-black p-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"bg-white border-4 border-black p-2 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]\">\n                <Users className=\"h-6 w-6 text-black\" />\n              </div>\n              <h3 className=\"text-xl font-black text-black\">SMART LISTS</h3>\n            </div>\n            <p className=\"text-black font-bold mb-3 text-sm leading-tight\">\n              Automatically combine ingredient lists and generate SMART shopping lists!\n            </p>\n            <button className=\"bg-black text-white font-black px-4 py-2 border-4 border-white shadow-[3px_3px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-sm\">\n              VIEW LISTS →\n            </button>\n          </div>\n\n          <div className=\"bg-yellow-400 border-4 border-black p-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"bg-white border-4 border-black p-2 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]\">\n                <Target className=\"h-6 w-6 text-black\" />\n              </div>\n              <h3 className=\"text-xl font-black text-black\">MEAL PLANNING</h3>\n            </div>\n            <p className=\"text-black font-bold mb-3 text-sm leading-tight\">\n              Plan entire meals with multiple courses and get a MASTER timeline!\n            </p>\n            <button className=\"bg-black text-white font-black px-4 py-2 border-4 border-white shadow-[3px_3px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-sm\">\n              PLAN MEALS →\n            </button>\n          </div>\n        </div>\n      </div>\n\n\n    </div>\n  );\n}\n\nfunction RecipesView() {\n  // Mock data for demonstration - in real app this would come from state/API\n  const hasRecipes = false; // Change to true to see recipes layout\n  const recipeCategories = [\n    { name: \"MAIN DISHES\", count: 0, color: \"bg-red-500\" },\n    { name: \"APPETIZERS\", count: 0, color: \"bg-blue-500\" },\n    { name: \"DESSERTS\", count: 0, color: \"bg-green-500\" },\n    { name: \"SIDES\", count: 0, color: \"bg-purple-500\" }\n  ];\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Header Section */}\n      <div className=\"relative\">\n        <div className=\"flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6\">\n          <div>\n            <h1 className=\"text-4xl lg:text-5xl font-black text-black transform -rotate-2 mb-2\">\n              MY RECIPES\n            </h1>\n            <p className=\"text-lg font-bold text-black/80\">\n              Organize and manage your culinary creations\n            </p>\n          </div>\n          <button className=\"px-6 py-3 bg-orange-500 text-white font-black text-lg border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform rotate-2 self-start lg:self-center\">\n            <Plus className=\"mr-2 h-5 w-5\" />\n            ADD RECIPE!\n          </button>\n        </div>\n      </div>\n\n      {/* Quick Stats */}\n      <div className=\"grid grid-cols-2 lg:grid-cols-4 gap-4\">\n        {recipeCategories.map((category, index) => (\n          <div key={category.name} className={`${category.color} border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform ${index % 2 === 0 ? 'rotate-1' : '-rotate-1'} hover:rotate-3 transition-transform cursor-pointer`}>\n            <div className=\"text-center\">\n              <div className=\"text-2xl font-black text-white mb-1\">{category.count}</div>\n              <div className=\"text-white font-bold text-sm\">{category.name}</div>\n            </div>\n          </div>\n        ))}\n      </div>\n\n      {/* Search and Filter Bar */}\n      <div className=\"bg-black border-4 border-white p-6 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform rotate-1\">\n        <div className=\"flex flex-col md:flex-row gap-4\">\n          <div className=\"flex-1\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <input\n                type=\"text\"\n                placeholder=\"SEARCH RECIPES...\"\n                className=\"w-full font-bold text-black placeholder-black/60 bg-transparent outline-none\"\n              />\n            </div>\n          </div>\n          <div className=\"flex gap-3\">\n            <button className=\"px-4 py-3 bg-yellow-400 text-black font-black border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\">\n              FILTER\n            </button>\n            <button className=\"px-4 py-3 bg-blue-400 text-black font-black border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\">\n              SORT\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main Content Area */}\n      {!hasRecipes ? (\n        /* Empty State */\n        <div className=\"bg-gradient-to-br from-yellow-300 to-yellow-400 border-6 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1\">\n          <div className=\"max-w-2xl mx-auto\">\n            <div className=\"bg-white border-4 border-black p-8 inline-block shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-8\">\n              <ChefHat className=\"h-20 w-20 text-black\" />\n            </div>\n            <h2 className=\"text-4xl font-black text-black mb-4 transform rotate-2\">\n              NO RECIPES YET!\n            </h2>\n            <p className=\"text-xl font-bold text-black mb-8 leading-relaxed\">\n              Your recipe collection is waiting to be filled! Start by adding your FIRST recipe\n              and unlock the power of coordinated cooking.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <button className=\"px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform hover:scale-105\">\n                <Plus className=\"mr-2 h-6 w-6\" />\n                ADD YOUR FIRST RECIPE!\n              </button>\n              <button className=\"px-8 py-4 bg-white text-black font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform hover:scale-105\">\n                IMPORT RECIPES\n              </button>\n            </div>\n          </div>\n        </div>\n      ) : (\n        /* Recipes Grid - This would show when hasRecipes is true */\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {/* Recipe cards would go here */}\n          <div className=\"bg-white border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n            <div className=\"bg-red-400 border-4 border-black p-4 mb-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <h3 className=\"font-black text-black text-lg\">SAMPLE RECIPE</h3>\n            </div>\n            <p className=\"font-bold text-black mb-4\">A delicious sample recipe description...</p>\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm font-bold text-black/70\">30 mins</span>\n              <button className=\"px-4 py-2 bg-green-500 text-white font-black border-4 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]\">\n                COOK!\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction SessionsView() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-5xl font-black text-black transform rotate-2\">COOKING SESSIONS</h2>\n        <button className=\"px-8 py-4 bg-blue-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform -rotate-2\">\n          <Zap className=\"mr-2 h-6 w-6\" />\n          NEW SESSION!\n        </button>\n      </div>\n\n      <div className=\"bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1\">\n        <div className=\"bg-blue-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-3 mb-6\">\n          <Clock className=\"h-16 w-16 text-black\" />\n        </div>\n        <h3 className=\"text-3xl font-black text-black mb-4\">NO SESSIONS YET!</h3>\n        <p className=\"text-xl font-bold text-black mb-8 max-w-md mx-auto\">\n          Create a cooking session to coordinate MULTIPLE dishes with PERFECT timing!\n        </p>\n        <button className=\"px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <Zap className=\"mr-2 h-6 w-6\" />\n          START YOUR FIRST SESSION!\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;;;AALA;;;;AAQe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAwC;IACjF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzD,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;;kCAEhB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;;;;;;;;;;kCAGjB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAoH;;;;;;8DAGlI,6LAAC;oDAAE,WAAU;8DAAyE;;;;;;;;;;;;;;;;;;8CAO1F,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,AAAC,mIAIX,OAHC,cAAc,cACV,gFACA;sDAEP;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,AAAC,oIAIX,OAHC,cAAc,YACV,iFACA;sDAEP;;;;;;sDAGD,6LAAC;4CACC,SAAS,IAAM,aAAa;4CAC5B,WAAW,AAAC,mIAIX,OAHC,cAAc,aACV,gFACA;sDAEP;;;;;;sDAKD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,SAAS,IAAM,sBAAsB;oDACrC,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAI,WAAU;;;;;;;;;;;;8DAGjB,6LAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzB,6LAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,6LAAC;;;;;oBAC9B,cAAc,2BAAa,6LAAC;;;;;oBAC5B,cAAc,4BAAc,6LAAC;;;;;;;;;;;YAI/B,oCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,OAAO;gDACP,QAAQ;;;;;;;;;;;sDAGZ,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;;;;;;;8CAIjD,6LAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAsE;;;;;;sDAGpF,6LAAC;4CAAG,WAAU;sDAA8D;;;;;;;;;;;;8CAK9E,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oDACJ,KAAI;oDACJ,KAAI;oDACJ,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;;;;;;;;;;;;sDAMhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;;;;;;8DAK9D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAAqC;;;;;;sEACnD,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;8EACJ,6LAAC;8EAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS,IAAM,sBAAsB;gCACrC,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;0BAST,6LAAC;gBAAI,WAAW,AAAC,uEAA2I,OAArE,mBAAmB,kBAAkB;0BAC1H,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BACC,SAAS,IAAM,oBAAoB,CAAC;4BACpC,WAAU;;8CAEV,6LAAC,mMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;8CACf,6LAAC;8CAAK;;;;;;8CACN,6LAAC,mNAAA,CAAA,YAAS;oCAAC,WAAW,AAAC,0CAA8E,OAArC,mBAAmB,eAAe;;;;;;;;;;;;sCAIpG,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAElB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAKhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAKhD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAKhD,6LAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAAgC;;;;;;8DAC9C,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;GAzPwB;KAAA;AA2PxB,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqF;;;;;;0CAGnG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAE,WAAU;kDAAwD;;;;;;kDAGrE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;gDAAK,WAAU;0DAAqC;;;;;;0DACrD,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAM1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;0CAGlD,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAsC;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtD,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAqE;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;kDAGhD,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAO,WAAU;kDAAqL;;;;;;;;;;;;0CAMzM,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;;;;;;0DAErB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAsC;;;;;;kEACpD,6LAAC;wDAAE,WAAU;kEAA+B;;;;;;;;;;;;;;;;;;kDAGhD,6LAAC;wCAAE,WAAU;kDAAoD;;;;;;kDAGjE,6LAAC;wCAAO,WAAU;kDAAqL;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7M,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAsE;;;;;;kCAIpF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;0DAEnB,6LAAC;gDAAG,WAAU;0DAAgC;;;;;;;;;;;;kDAEhD,6LAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAG/D,6LAAC;wCAAO,WAAU;kDAAyJ;;;;;;;;;;;;0CAK7K,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;0DAEpB,6LAAC;gDAAG,WAAU;0DAAgC;;;;;;;;;;;;kDAEhD,6LAAC;wCAAE,WAAU;kDAAkD;;;;;;kDAG/D,6LAAC;wCAAO,WAAU;kDAAyJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvL;MA3IS;AA6IT,SAAS;IACP,2EAA2E;IAC3E,MAAM,aAAa,OAAO,uCAAuC;IACjE,MAAM,mBAAmB;QACvB;YAAE,MAAM;YAAe,OAAO;YAAG,OAAO;QAAa;QACrD;YAAE,MAAM;YAAc,OAAO;YAAG,OAAO;QAAc;QACrD;YAAE,MAAM;YAAY,OAAO;YAAG,OAAO;QAAe;QACpD;YAAE,MAAM;YAAS,OAAO;YAAG,OAAO;QAAgB;KACnD;IAED,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsE;;;;;;8CAGpF,6LAAC;oCAAE,WAAU;8CAAkC;;;;;;;;;;;;sCAIjD,6LAAC;4BAAO,WAAU;;8CAChB,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAOvC,6LAAC;gBAAI,WAAU;0BACZ,iBAAiB,GAAG,CAAC,CAAC,UAAU,sBAC/B,6LAAC;wBAAwB,WAAW,AAAC,GAA+F,OAA7F,SAAS,KAAK,EAAC,gFAAyH,OAA3C,QAAQ,MAAM,IAAI,aAAa,aAAY;kCAC7K,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAuC,SAAS,KAAK;;;;;;8CACpE,6LAAC;oCAAI,WAAU;8CAAgC,SAAS,IAAI;;;;;;;;;;;;uBAHtD,SAAS,IAAI;;;;;;;;;;0BAU3B,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,MAAK;oCACL,aAAY;oCACZ,WAAU;;;;;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;8CAA4L;;;;;;8CAG9M,6LAAC;oCAAO,WAAU;8CAA0L;;;;;;;;;;;;;;;;;;;;;;;YAQjN,uCACC,eAAe,iBACf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+MAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;;;;;;sCAErB,6LAAC;4BAAG,WAAU;sCAAyD;;;;;;sCAGvE,6LAAC;4BAAE,WAAU;sCAAoD;;;;;;sCAIjE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAO,WAAU;;sDAChB,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGnC,6LAAC;oCAAO,WAAU;8CAAmN;;;;;;;;;;;;;;;;;;;;;;uBAO3O,0DAA0D,iBAC1D;;;;;;;AAkBR;MA/GS;AAiHT,SAAS;IACP,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAClE,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKpC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,6LAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,6LAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAGlE,6LAAC;wBAAO,WAAU;;0CAChB,6LAAC,mMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM1C;MA1BS", "debugId": null}}]}