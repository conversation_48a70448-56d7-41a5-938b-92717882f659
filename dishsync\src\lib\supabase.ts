import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database table names
export const TABLES = {
  USERS: 'users',
  INGREDIENTS: 'ingredients',
  RECIPES: 'recipes',
  RECIPE_INGREDIENTS: 'recipe_ingredients',
  COOKING_STEPS: 'cooking_steps',
  COOKING_SESSIONS: 'cooking_sessions',
  SESSION_STEPS: 'session_steps',
  TIMERS: 'timers',
  SHOPPING_LIST_ITEMS: 'shopping_list_items',
} as const;

// Helper function to handle Supabase errors
export function handleSupabaseError(error: any) {
  console.error('Supabase error:', error);
  return {
    error: error.message || 'An unexpected error occurred',
    data: null,
  };
}

// Auth helpers
export async function getCurrentUser() {
  const { data: { user }, error } = await supabase.auth.getUser();
  if (error) return handleSupabaseError(error);
  return { data: user, error: null };
}

export async function signOut() {
  const { error } = await supabase.auth.signOut();
  if (error) return handleSupabaseError(error);
  return { data: true, error: null };
}
