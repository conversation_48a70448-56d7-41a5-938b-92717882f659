{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/test/dishsync/src/components/GettingStartedModal.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { X, ChefHat, Clock, Users, Zap, ArrowRight, ArrowLeft } from 'lucide-react';\nimport Image from 'next/image';\n\ninterface GettingStartedModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nexport default function GettingStartedModal({ isOpen, onClose }: GettingStartedModalProps) {\n  const [currentStep, setCurrentStep] = useState(0);\n\n  const steps = [\n    {\n      title: \"WELCOME TO DISHSYNC!\",\n      subtitle: \"Your Ultimate Cooking Companion\",\n      content: \"DishSync is the REVOLUTIONARY multi-dish cooking assistant that will transform your kitchen experience! Say goodbye to burnt food and timing disasters!\",\n      image: \"/assets/chef.png\",\n      bgColor: \"bg-yellow-400\",\n      textColor: \"text-black\"\n    },\n    {\n      title: \"ADD YOUR RECIPES\",\n      subtitle: \"Build Your Recipe Collection\",\n      content: \"Start by adding your favorite recipes! Include ingredients, cooking steps, prep time, and cooking instructions. The more detailed, the better DishSync can help you!\",\n      icon: <ChefHat className=\"h-16 w-16\" />,\n      bgColor: \"bg-blue-400\",\n      textColor: \"text-black\"\n    },\n    {\n      title: \"CREATE COOKING SESSIONS\",\n      subtitle: \"Coordinate Multiple Dishes\",\n      content: \"Select multiple recipes for your meal and let DishSync create the PERFECT timeline! It will tell you exactly when to start each step for flawless coordination!\",\n      icon: <Clock className=\"h-16 w-16\" />,\n      bgColor: \"bg-green-400\",\n      textColor: \"text-black\"\n    },\n    {\n      title: \"PREP MODE MAGIC\",\n      subtitle: \"Consolidate Your Preparation\",\n      content: \"DishSync automatically groups similar prep tasks together! Chop all your onions at once, measure all spices together - MAXIMUM efficiency!\",\n      icon: <Users className=\"h-16 w-16\" />,\n      bgColor: \"bg-purple-400\",\n      textColor: \"text-black\"\n    },\n    {\n      title: \"COOK LIKE A PRO!\",\n      subtitle: \"Follow the Perfect Timeline\",\n      content: \"Enter Cook Mode and follow the step-by-step timeline! Real-time timers, notifications, and guidance ensure PERFECT results every time!\",\n      icon: <Zap className=\"h-16 w-16\" />,\n      bgColor: \"bg-red-400\",\n      textColor: \"text-white\"\n    }\n  ];\n\n  if (!isOpen) return null;\n\n  const currentStepData = steps[currentStep];\n  const isLastStep = currentStep === steps.length - 1;\n  const isFirstStep = currentStep === 0;\n\n  const nextStep = () => {\n    if (!isLastStep) {\n      setCurrentStep(currentStep + 1);\n    }\n  };\n\n  const prevStep = () => {\n    if (!isFirstStep) {\n      setCurrentStep(currentStep - 1);\n    }\n  };\n\n  const handleClose = () => {\n    setCurrentStep(0);\n    onClose();\n  };\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className={`${currentStepData.bgColor} border-8 border-black shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] max-w-4xl w-full max-h-[90vh] overflow-y-auto transform rotate-1`}>\n        {/* Header */}\n        <div className=\"flex justify-between items-center p-6 border-b-4 border-black\">\n          <div className=\"flex items-center space-x-4\">\n            <div className=\"bg-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <Image \n                src=\"/assets/logo.png\" \n                alt=\"DishSync Logo\" \n                width={32} \n                height={32}\n              />\n            </div>\n            <h2 className={`text-2xl font-black ${currentStepData.textColor}`}>\n              GETTING STARTED GUIDE\n            </h2>\n          </div>\n          <button\n            onClick={handleClose}\n            className=\"bg-red-500 text-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n          >\n            <X className=\"h-6 w-6\" />\n          </button>\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"p-6 border-b-4 border-black\">\n          <div className=\"flex justify-between items-center mb-4\">\n            <span className={`font-black ${currentStepData.textColor}`}>\n              STEP {currentStep + 1} OF {steps.length}\n            </span>\n            <span className={`font-black ${currentStepData.textColor}`}>\n              {Math.round(((currentStep + 1) / steps.length) * 100)}%\n            </span>\n          </div>\n          <div className=\"w-full bg-black border-2 border-black h-4\">\n            <div\n              className=\"bg-white h-full transition-all duration-300 border-r-2 border-black\"\n              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}\n            />\n          </div>\n        </div>\n\n        {/* Content */}\n        <div className=\"p-8\">\n          <div className=\"text-center mb-8\">\n            <h1 className={`text-5xl font-black ${currentStepData.textColor} mb-4 transform -rotate-2`}>\n              {currentStepData.title}\n            </h1>\n            <h2 className={`text-2xl font-bold ${currentStepData.textColor} transform rotate-1`}>\n              {currentStepData.subtitle}\n            </h2>\n          </div>\n\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8 items-center\">\n            {/* Image/Icon */}\n            <div className=\"flex justify-center\">\n              {currentStepData.image ? (\n                <div className=\"bg-white border-4 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3\">\n                  <Image\n                    src={currentStepData.image}\n                    alt=\"Chef illustration\"\n                    width={300}\n                    height={300}\n                    className=\"rounded-lg\"\n                  />\n                </div>\n              ) : (\n                <div className=\"bg-white border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform rotate-3\">\n                  {currentStepData.icon}\n                </div>\n              )}\n            </div>\n\n            {/* Content */}\n            <div className=\"space-y-6\">\n              <div className=\"bg-white border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-1\">\n                <p className=\"text-xl font-bold text-black leading-relaxed\">\n                  {currentStepData.content}\n                </p>\n              </div>\n\n              {/* Step-specific tips */}\n              {currentStep === 0 && (\n                <div className=\"bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                  <p className=\"text-white font-bold\">\n                    💡 TIP: DishSync works best when you have multiple recipes to coordinate!\n                  </p>\n                </div>\n              )}\n\n              {currentStep === 1 && (\n                <div className=\"bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                  <p className=\"text-white font-bold\">\n                    💡 TIP: Include prep times and cooking temperatures for best results!\n                  </p>\n                </div>\n              )}\n\n              {currentStep === 2 && (\n                <div className=\"bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                  <p className=\"text-white font-bold\">\n                    💡 TIP: DishSync automatically calculates the optimal start times!\n                  </p>\n                </div>\n              )}\n\n              {currentStep === 3 && (\n                <div className=\"bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                  <p className=\"text-white font-bold\">\n                    💡 TIP: Prep Mode saves you time by grouping similar tasks!\n                  </p>\n                </div>\n              )}\n\n              {currentStep === 4 && (\n                <div className=\"bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\">\n                  <p className=\"text-white font-bold\">\n                    💡 TIP: Use the built-in timers to never overcook anything again!\n                  </p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <div className=\"flex justify-between items-center p-6 border-t-4 border-black\">\n          <button\n            onClick={prevStep}\n            disabled={isFirstStep}\n            className={`flex items-center px-6 py-3 font-black text-lg border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all ${\n              isFirstStep\n                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'\n                : 'bg-white text-black hover:translate-x-1 hover:translate-y-1 hover:shadow-none'\n            }`}\n          >\n            <ArrowLeft className=\"mr-2 h-5 w-5\" />\n            BACK\n          </button>\n\n          <div className=\"flex space-x-2\">\n            {steps.map((_, index) => (\n              <button\n                key={index}\n                onClick={() => setCurrentStep(index)}\n                className={`w-4 h-4 border-2 border-black transition-all ${\n                  index === currentStep\n                    ? 'bg-black'\n                    : 'bg-white hover:bg-gray-200'\n                }`}\n              />\n            ))}\n          </div>\n\n          {isLastStep ? (\n            <button\n              onClick={handleClose}\n              className=\"flex items-center px-6 py-3 bg-green-500 text-white font-black text-lg border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n            >\n              START COOKING!\n              <Zap className=\"ml-2 h-5 w-5\" />\n            </button>\n          ) : (\n            <button\n              onClick={nextStep}\n              className=\"flex items-center px-6 py-3 bg-blue-500 text-white font-black text-lg border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all\"\n            >\n              NEXT\n              <ArrowRight className=\"ml-2 h-5 w-5\" />\n            </button>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAJA;;;;;AAWe,SAAS,oBAAoB,EAAE,MAAM,EAAE,OAAO,EAA4B;IACvF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,QAAQ;QACZ;YACE,OAAO;YACP,UAAU;YACV,SAAS;YACT,OAAO;YACP,SAAS;YACT,WAAW;QACb;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;YACT,oBAAM,8OAAC,4MAAA,CAAA,UAAO;gBAAC,WAAU;;;;;;YACzB,SAAS;YACT,WAAW;QACb;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;YACT,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,SAAS;YACT,WAAW;QACb;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;YACT,oBAAM,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;YACvB,SAAS;YACT,WAAW;QACb;QACA;YACE,OAAO;YACP,UAAU;YACV,SAAS;YACT,oBAAM,8OAAC,gMAAA,CAAA,MAAG;gBAAC,WAAU;;;;;;YACrB,SAAS;YACT,WAAW;QACb;KACD;IAED,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,kBAAkB,KAAK,CAAC,YAAY;IAC1C,MAAM,aAAa,gBAAgB,MAAM,MAAM,GAAG;IAClD,MAAM,cAAc,gBAAgB;IAEpC,MAAM,WAAW;QACf,IAAI,CAAC,YAAY;YACf,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,WAAW;QACf,IAAI,CAAC,aAAa;YAChB,eAAe,cAAc;QAC/B;IACF;IAEA,MAAM,cAAc;QAClB,eAAe;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,GAAG,gBAAgB,OAAO,CAAC,gIAAgI,CAAC;;8BAE1K,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;;;;;;8CAGZ,8OAAC;oCAAG,WAAW,CAAC,oBAAoB,EAAE,gBAAgB,SAAS,EAAE;8CAAE;;;;;;;;;;;;sCAIrE,8OAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,WAAW,EAAE,gBAAgB,SAAS,EAAE;;wCAAE;wCACpD,cAAc;wCAAE;wCAAK,MAAM,MAAM;;;;;;;8CAEzC,8OAAC;oCAAK,WAAW,CAAC,WAAW,EAAE,gBAAgB,SAAS,EAAE;;wCACvD,KAAK,KAAK,CAAC,AAAC,CAAC,cAAc,CAAC,IAAI,MAAM,MAAM,GAAI;wCAAK;;;;;;;;;;;;;sCAG1D,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,AAAC,CAAC,cAAc,CAAC,IAAI,MAAM,MAAM,GAAI,IAAI,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;8BAMrE,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAW,CAAC,oBAAoB,EAAE,gBAAgB,SAAS,CAAC,yBAAyB,CAAC;8CACvF,gBAAgB,KAAK;;;;;;8CAExB,8OAAC;oCAAG,WAAW,CAAC,mBAAmB,EAAE,gBAAgB,SAAS,CAAC,mBAAmB,CAAC;8CAChF,gBAAgB,QAAQ;;;;;;;;;;;;sCAI7B,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,KAAK,iBACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;4CACJ,KAAK,gBAAgB,KAAK;4CAC1B,KAAI;4CACJ,OAAO;4CACP,QAAQ;4CACR,WAAU;;;;;;;;;;6DAId,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,IAAI;;;;;;;;;;;8CAM3B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DACV,gBAAgB,OAAO;;;;;;;;;;;wCAK3B,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;wCAMvC,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;wCAMvC,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;wCAMvC,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;wCAMvC,gBAAgB,mBACf,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAU9C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,2HAA2H,EACrI,cACI,iDACA,iFACJ;;8CAEF,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAIxC,8OAAC;4BAAI,WAAU;sCACZ,MAAM,GAAG,CAAC,CAAC,GAAG,sBACb,8OAAC;oCAEC,SAAS,IAAM,eAAe;oCAC9B,WAAW,CAAC,6CAA6C,EACvD,UAAU,cACN,aACA,8BACJ;mCANG;;;;;;;;;;wBAWV,2BACC,8OAAC;4BACC,SAAS;4BACT,WAAU;;gCACX;8CAEC,8OAAC,gMAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;iDAGjB,8OAAC;4BACC,SAAS;4BACT,WAAU;;gCACX;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC", "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/test/dishsync/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles } from 'lucide-react';\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport GettingStartedModal from '@/components/GettingStartedModal';\n\nexport default function Home() {\n  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');\n  const [showGettingStarted, setShowGettingStarted] = useState(false);\n\n  return (\n    <div className=\"min-h-screen bg-yellow-300\">\n      {/* Header */}\n      <header className=\"bg-black border-b-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)]\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-20\">\n            <div className=\"flex items-center space-x-4\">\n              <Image\n                src=\"/assets/logo.png\"\n                alt=\"DishSync Logo\"\n                width={48}\n                height={48}\n                className=\"rounded-lg border-2 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]\"\n              />\n              <h1 className=\"text-3xl font-black text-white tracking-tight\">DISHSYNC</h1>\n            </div>\n            <nav className=\"flex space-x-4\">\n              <button\n                onClick={() => setActiveTab('dashboard')}\n                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${\n                  activeTab === 'dashboard'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'\n                }`}\n              >\n                DASHBOARD\n              </button>\n              <button\n                onClick={() => setActiveTab('recipes')}\n                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${\n                  activeTab === 'recipes'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'\n                }`}\n              >\n                RECIPES\n              </button>\n              <button\n                onClick={() => setActiveTab('sessions')}\n                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${\n                  activeTab === 'sessions'\n                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'\n                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'\n                }`}\n              >\n                SESSIONS\n              </button>\n              <button\n                onClick={() => setShowGettingStarted(true)}\n                className=\"px-6 py-3 font-black text-lg border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none hover:bg-green-600\"\n              >\n                HELP\n              </button>\n            </nav>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12\">\n        {activeTab === 'dashboard' && <DashboardView />}\n        {activeTab === 'recipes' && <RecipesView />}\n        {activeTab === 'sessions' && <SessionsView />}\n      </main>\n\n      {/* Getting Started Modal */}\n      <GettingStartedModal\n        isOpen={showGettingStarted}\n        onClose={() => setShowGettingStarted(false)}\n      />\n    </div>\n  );\n}\n\nfunction DashboardView() {\n  return (\n    <div className=\"space-y-12\">\n      {/* Welcome Section */}\n      <div className=\"text-center\">\n        <h2 className=\"text-6xl font-black text-black mb-6 tracking-tight transform -rotate-1\">\n          WELCOME TO DISHSYNC!\n        </h2>\n        <div className=\"bg-black border-4 border-white p-8 shadow-[12px_12px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto\">\n          <p className=\"text-2xl font-bold text-white leading-relaxed\">\n            The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency.\n            Never worry about timing multiple dishes again!\n          </p>\n          <div className=\"flex justify-center mt-6\">\n            <Sparkles className=\"h-8 w-8 text-yellow-400 animate-pulse\" />\n          </div>\n        </div>\n      </div>\n\n      {/* Feature Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n        <div className=\"bg-blue-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <Clock className=\"h-8 w-8 text-black\" />\n            </div>\n            <h3 className=\"text-2xl font-black text-black\">TIMER SYNC</h3>\n          </div>\n          <p className=\"text-black font-bold mb-6 text-lg leading-tight\">\n            Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!\n          </p>\n          <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors\">\n            START SESSION →\n          </button>\n        </div>\n\n        <div className=\"bg-green-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <ChefHat className=\"h-8 w-8 text-black\" />\n            </div>\n            <h3 className=\"text-2xl font-black text-black\">PREP & COOK</h3>\n          </div>\n          <p className=\"text-black font-bold mb-6 text-lg leading-tight\">\n            Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!\n          </p>\n          <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors\">\n            MANAGE RECIPES →\n          </button>\n        </div>\n\n        <div className=\"bg-purple-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <div className=\"flex items-center space-x-4 mb-6\">\n            <div className=\"bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]\">\n              <Users className=\"h-8 w-8 text-black\" />\n            </div>\n            <h3 className=\"text-2xl font-black text-black\">SMART LISTS</h3>\n          </div>\n          <p className=\"text-black font-bold mb-6 text-lg leading-tight\">\n            Automatically combine ingredient lists and generate SMART shopping lists!\n          </p>\n          <button className=\"bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors\">\n            VIEW LISTS →\n          </button>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-red-500 border-4 border-black p-8 shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1\">\n        <h3 className=\"text-3xl font-black text-white mb-8 text-center transform rotate-1\">QUICK ACTIONS!</h3>\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6\">\n          <button className=\"flex items-center justify-center px-6 py-4 bg-yellow-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3\">\n            <Plus className=\"mr-2 h-5 w-5\" />\n            ADD RECIPE\n          </button>\n          <button className=\"flex items-center justify-center px-6 py-4 bg-blue-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3\">\n            <Clock className=\"mr-2 h-5 w-5\" />\n            NEW SESSION\n          </button>\n          <button className=\"flex items-center justify-center px-6 py-4 bg-green-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3\">\n            <ChefHat className=\"mr-2 h-5 w-5\" />\n            BROWSE RECIPES\n          </button>\n          <button className=\"flex items-center justify-center px-6 py-4 bg-purple-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3\">\n            <Target className=\"mr-2 h-5 w-5\" />\n            DASHBOARD\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\nfunction RecipesView() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-5xl font-black text-black transform -rotate-2\">MY RECIPES</h2>\n        <button className=\"px-8 py-4 bg-orange-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform rotate-2\">\n          <Plus className=\"mr-2 h-6 w-6\" />\n          ADD RECIPE!\n        </button>\n      </div>\n\n      <div className=\"bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1\">\n        <div className=\"bg-yellow-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-6\">\n          <ChefHat className=\"h-16 w-16 text-black\" />\n        </div>\n        <h3 className=\"text-3xl font-black text-black mb-4\">NO RECIPES YET!</h3>\n        <p className=\"text-xl font-bold text-black mb-8 max-w-md mx-auto\">\n          Start by adding your FIRST recipe to begin coordinating your cooking like a PRO!\n        </p>\n        <button className=\"px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <Plus className=\"mr-2 h-6 w-6\" />\n          ADD YOUR FIRST RECIPE!\n        </button>\n      </div>\n    </div>\n  );\n}\n\nfunction SessionsView() {\n  return (\n    <div className=\"space-y-8\">\n      <div className=\"flex justify-between items-center\">\n        <h2 className=\"text-5xl font-black text-black transform rotate-2\">COOKING SESSIONS</h2>\n        <button className=\"px-8 py-4 bg-blue-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform -rotate-2\">\n          <Zap className=\"mr-2 h-6 w-6\" />\n          NEW SESSION!\n        </button>\n      </div>\n\n      <div className=\"bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1\">\n        <div className=\"bg-blue-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-3 mb-6\">\n          <Clock className=\"h-16 w-16 text-black\" />\n        </div>\n        <h3 className=\"text-3xl font-black text-black mb-4\">NO SESSIONS YET!</h3>\n        <p className=\"text-xl font-bold text-black mb-8 max-w-md mx-auto\">\n          Create a cooking session to coordinate MULTIPLE dishes with PERFECT timing!\n        </p>\n        <button className=\"px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all\">\n          <Zap className=\"mr-2 h-6 w-6\" />\n          START YOUR FIRST SESSION!\n        </button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAEA;AACA;AANA;;;;;;AAQe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwC;IACjF,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,OAAO;wCACP,QAAQ;wCACR,WAAU;;;;;;kDAEZ,8OAAC;wCAAG,WAAU;kDAAgD;;;;;;;;;;;;0CAEhE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,sIAAsI,EAChJ,cAAc,cACV,uEACA,wFACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,sIAAsI,EAChJ,cAAc,YACV,uEACA,wFACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,aAAa;wCAC5B,WAAW,CAAC,sIAAsI,EAChJ,cAAc,aACV,uEACA,wFACJ;kDACH;;;;;;kDAGD,8OAAC;wCACC,SAAS,IAAM,sBAAsB;wCACrC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAST,8OAAC;gBAAK,WAAU;;oBACb,cAAc,6BAAe,8OAAC;;;;;oBAC9B,cAAc,2BAAa,8OAAC;;;;;oBAC5B,cAAc,4BAAc,8OAAC;;;;;;;;;;;0BAIhC,8OAAC,yIAAA,CAAA,UAAmB;gBAClB,QAAQ;gBACR,SAAS,IAAM,sBAAsB;;;;;;;;;;;;AAI7C;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyE;;;;;;kCAGvF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAgD;;;;;;0CAI7D,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;0BAM1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAG/D,8OAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;kCAKrK,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;;kDAErB,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAG/D,8OAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;kCAKrK,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,8OAAC;wCAAG,WAAU;kDAAiC;;;;;;;;;;;;0CAEjD,8OAAC;gCAAE,WAAU;0CAAkD;;;;;;0CAG/D,8OAAC;gCAAO,WAAU;0CAAiJ;;;;;;;;;;;;;;;;;;0BAOvK,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqE;;;;;;kCACnF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,kMAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGnC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGpC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,4MAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGtC,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,sMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqD;;;;;;kCACnE,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4MAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;;;;;;kCAErB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAGlE,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM3C;AAEA,SAAS;IACP,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoD;;;;;;kCAClE,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKpC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;;;;;;kCAEnB,8OAAC;wBAAG,WAAU;kCAAsC;;;;;;kCACpD,8OAAC;wBAAE,WAAU;kCAAqD;;;;;;kCAGlE,8OAAC;wBAAO,WAAU;;0CAChB,8OAAC,gMAAA,CAAA,MAAG;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}]}