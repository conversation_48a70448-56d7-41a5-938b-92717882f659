# 🍳 DishSync - Smart Multi-Dish Cooking Assistant

**The ULTIMATE cooking companion that coordinates multiple dishes with PERFECT timing!**

DishSync is a revolutionary web application that helps you cook multiple dishes simultaneously by creating optimized timelines, consolidating prep work, and providing real-time cooking guidance.

## ✨ Features

- **🕐 Multi-Dish Timer Sync**: Coordinate multiple recipes with intelligent timeline optimization
- **👨‍🍳 Prep & Cook Modes**: Consolidate preparation tasks and get guided cooking instructions
- **📝 Ingredient Aggregation**: Automatically combine shopping lists across recipes
- **⏰ Real-time Timers**: Built-in timers with notifications for perfect timing
- **📱 Kitchen Dashboard**: Visual progress tracking for all your dishes
- **🎨 Neo-Brutalist UI**: Bold, energetic design that makes cooking fun!

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- A Supabase account (free at [supabase.com](https://supabase.com))

### Installation

1. **Clone the repository**
   ```bash
   git clone <your-repo-url>
   cd dishsync
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   ```bash
   npm run setup-supabase
   ```
   This interactive script will:
   - Prompt you for your Supabase credentials
   - Create the `.env.local` file
   - Provide instructions for database setup

4. **Set up the database**
   - Go to your Supabase project dashboard
   - Navigate to the SQL Editor
   - Copy and paste the contents of `database/schema.sql`
   - Run the SQL to create all tables and policies
   - (Optional) Run `database/sample_data.sql` for sample data

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🗄️ Database Setup

DishSync uses Supabase as its backend. The database schema includes:

- **Users**: User accounts and profiles
- **Recipes**: Recipe storage with ingredients and cooking steps
- **Cooking Sessions**: Multi-dish cooking coordination
- **Timers**: Real-time cooking timers
- **Shopping Lists**: Aggregated ingredient lists

### Manual Database Setup

If you prefer to set up the database manually:

1. Create a new Supabase project
2. Copy your project URL and anon key
3. Create `.env.local` with your credentials:
   ```env
   NEXT_PUBLIC_SUPABASE_URL=your-project-url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
   ```
4. Run the SQL files in the Supabase SQL Editor:
   - `database/schema.sql` (required)
   - `database/sample_data.sql` (optional)

## 🎯 How to Use DishSync

### 1. Add Your Recipes
- Click "ADD RECIPE" to create your first recipe
- Include ingredients, cooking steps, prep times, and temperatures
- Mark steps as prep or cooking steps
- Add equipment requirements

### 2. Create a Cooking Session
- Select multiple recipes for your meal
- DishSync automatically creates an optimized timeline
- Review the coordination plan and shopping list

### 3. Prep Mode
- Follow consolidated prep instructions
- Complete similar tasks together (chopping, measuring, etc.)
- Save time with intelligent task grouping

### 4. Cook Mode
- Follow the step-by-step timeline
- Use built-in timers for perfect timing
- Get real-time notifications and guidance

## 🛠️ Development

### Project Structure

```
dishsync/
├── src/
│   ├── app/                 # Next.js app router
│   ├── components/          # React components
│   ├── hooks/              # Custom React hooks
│   ├── lib/                # Database and utility functions
│   ├── types/              # TypeScript type definitions
│   └── utils/              # Helper functions
├── database/               # SQL schema and sample data
├── public/assets/          # Images and static assets
└── scripts/               # Setup and utility scripts
```

### Key Technologies

- **Frontend**: Next.js 15, React 19, TypeScript
- **Styling**: Tailwind CSS (Neo-Brutalist design)
- **Backend**: Supabase (PostgreSQL + Auth + Real-time)
- **Icons**: Lucide React
- **Date Handling**: date-fns

### Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run setup-supabase` - Interactive Supabase setup

## 🎨 Design Philosophy

DishSync uses a **Neo-Brutalist** design approach:

- **Bold Colors**: Bright yellows, reds, blues, and greens
- **Heavy Borders**: Thick black borders on all elements
- **Drop Shadows**: Prominent shadows for depth
- **Typography**: Black, bold fonts for maximum impact
- **Playful Interactions**: Hover effects with movement and rotation

This energetic design makes cooking feel fun and exciting!

## 🤝 Contributing

We welcome contributions! Please:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📝 License

This project is licensed under the MIT License.

## 🆘 Support

Having issues? Check out:

- The built-in Getting Started guide (click HELP in the app)
- [Supabase Documentation](https://supabase.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)

## 🎉 Acknowledgments

- Chef illustrations and timer graphics
- Neo-Brutalist design inspiration
- The amazing open-source community

---

**Happy Cooking! 👨‍🍳✨**

*Made with ❤️ for home cooks who want to level up their kitchen game!*
