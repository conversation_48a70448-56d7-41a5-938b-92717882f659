'use client';

import { useState } from 'react';
import { ChefHat, Clock, Users, Plus } from 'lucide-react';
import Link from 'next/link';

export default function Home() {
  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-red-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <ChefHat className="h-8 w-8 text-orange-600" />
              <h1 className="text-2xl font-bold text-gray-900">DishSync</h1>
            </div>
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'dashboard'
                    ? 'bg-orange-100 text-orange-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Dashboard
              </button>
              <button
                onClick={() => setActiveTab('recipes')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'recipes'
                    ? 'bg-orange-100 text-orange-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Recipes
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                  activeTab === 'sessions'
                    ? 'bg-orange-100 text-orange-700'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Sessions
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && <DashboardView />}
        {activeTab === 'recipes' && <RecipesView />}
        {activeTab === 'sessions' && <SessionsView />}
      </main>
    </div>
  );
}

function DashboardView() {
  return (
    <div className="space-y-8">
      {/* Welcome Section */}
      <div className="text-center">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">
          Welcome to DishSync
        </h2>
        <p className="text-lg text-gray-600 max-w-2xl mx-auto">
          The smart multi-dish cooking assistant that coordinates your recipes for maximum efficiency.
          Never worry about timing multiple dishes again!
        </p>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center space-x-3 mb-4">
            <Clock className="h-8 w-8 text-blue-600" />
            <h3 className="text-xl font-semibold text-gray-900">Multi-Dish Timer Sync</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Input all your dishes and get a coordinated timeline showing when to start each step for perfect timing.
          </p>
          <button className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium">
            Start Cooking Session
            <Plus className="ml-1 h-4 w-4" />
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center space-x-3 mb-4">
            <ChefHat className="h-8 w-8 text-green-600" />
            <h3 className="text-xl font-semibold text-gray-900">Prep & Cook Modes</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Consolidate overlapping prep steps and get guided cooking with real-time prompts.
          </p>
          <button className="inline-flex items-center text-green-600 hover:text-green-700 font-medium">
            Manage Recipes
            <Plus className="ml-1 h-4 w-4" />
          </button>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
          <div className="flex items-center space-x-3 mb-4">
            <Users className="h-8 w-8 text-purple-600" />
            <h3 className="text-xl font-semibold text-gray-900">Ingredient Aggregation</h3>
          </div>
          <p className="text-gray-600 mb-4">
            Automatically combine ingredient lists across recipes and generate smart shopping lists.
          </p>
          <button className="inline-flex items-center text-purple-600 hover:text-purple-700 font-medium">
            View Shopping Lists
            <Plus className="ml-1 h-4 w-4" />
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <button className="flex items-center justify-center px-4 py-3 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors">
            <Plus className="mr-2 h-4 w-4" />
            Add Recipe
          </button>
          <button className="flex items-center justify-center px-4 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <Clock className="mr-2 h-4 w-4" />
            New Session
          </button>
          <button className="flex items-center justify-center px-4 py-3 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
            <ChefHat className="mr-2 h-4 w-4" />
            Browse Recipes
          </button>
          <button className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
            <Users className="mr-2 h-4 w-4" />
            Kitchen Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}

function RecipesView() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">My Recipes</h2>
        <button className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors">
          <Plus className="mr-2 h-4 w-4" />
          Add Recipe
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <ChefHat className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No recipes yet</h3>
        <p className="text-gray-600 mb-4">
          Start by adding your first recipe to begin coordinating your cooking.
        </p>
        <button className="inline-flex items-center px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors">
          <Plus className="mr-2 h-4 w-4" />
          Add Your First Recipe
        </button>
      </div>
    </div>
  );
}

function SessionsView() {
  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Cooking Sessions</h2>
        <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          <Plus className="mr-2 h-4 w-4" />
          New Session
        </button>
      </div>

      <div className="bg-white rounded-lg shadow-md p-8 text-center">
        <Clock className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No cooking sessions</h3>
        <p className="text-gray-600 mb-4">
          Create a cooking session to coordinate multiple dishes with perfect timing.
        </p>
        <button className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
          <Plus className="mr-2 h-4 w-4" />
          Start Your First Session
        </button>
      </div>
    </div>
  );
}
