'use client';

import { useState } from 'react';
import { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles, X, ChevronUp, BarChart3, HelpCircle } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';


export default function Home() {
  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');
  const [showGettingStarted, setShowGettingStarted] = useState(false);
  const [showQuickActions, setShowQuickActions] = useState(false);

  return (
    <div className="min-h-screen bg-yellow-300">
      {/* Header */}
      <header className="bg-black border-b-8 border-yellow-400 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-red-500 via-yellow-400 to-blue-500"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            {/* Logo Section */}
            <div className="flex items-center space-x-6">
              <div className="relative">
                <div className="bg-yellow-400 border-4 border-white p-3 shadow-[6px_6px_0px_0px_rgba(255,255,255,1)] transform rotate-3 hover:rotate-6 transition-transform">
                  <Image
                    src="/assets/logo.png"
                    alt="DishSync Logo"
                    width={56}
                    height={56}
                    className="rounded-lg"
                  />
                </div>
              </div>
              <div className="flex flex-col">
                <h1 className="text-4xl lg:text-5xl font-black text-white tracking-tight transform -rotate-1 hover:rotate-0 transition-transform">
                  DISHSYNC
                </h1>
                <p className="text-yellow-400 font-bold text-sm lg:text-base transform rotate-1 mt-1">
                  COOK LIKE A PRO!
                </p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex items-center space-x-3">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${
                  activeTab === 'dashboard'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'
                }`}
              >
                DASHBOARD
              </button>
              <button
                onClick={() => setActiveTab('recipes')}
                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:-rotate-1 ${
                  activeTab === 'recipes'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] -rotate-1'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-blue-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'
                }`}
              >
                RECIPES
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${
                  activeTab === 'sessions'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-purple-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'
                }`}
              >
                SESSIONS
              </button>

              {/* Help Button - Special Styling */}
              <div className="relative ml-2">
                <button
                  onClick={() => setShowGettingStarted(true)}
                  className="px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:scale-110 hover:rotate-3 hover:bg-green-400 hover:shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] relative overflow-hidden"
                >
                  <span className="relative z-10">HELP</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 opacity-0 hover:opacity-100 transition-opacity"></div>
                </button>
                {/* Pulsing indicator */}
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 border-2 border-white rounded-full animate-pulse"></div>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {activeTab === 'dashboard' && <DashboardView />}
        {activeTab === 'recipes' && <RecipesView />}
        {activeTab === 'sessions' && <SessionsView />}
      </main>

      {/* Getting Started Modal */}
      {showGettingStarted && (
        <div className="fixed inset-0 backdrop-blur-md bg-black/20 flex items-center justify-center z-50 p-4">
          <div className="bg-yellow-400 border-8 border-black shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] w-full max-w-5xl h-[85vh] flex flex-col transform rotate-1">
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b-4 border-black flex-shrink-0">
              <div className="flex items-center space-x-4">
                <div className="bg-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                  <Image
                    src="/assets/logo.png"
                    alt="DishSync Logo"
                    width={32}
                    height={32}
                  />
                </div>
                <h2 className="text-2xl font-black text-black">
                  GETTING STARTED GUIDE
                </h2>
              </div>
              <button
                onClick={() => setShowGettingStarted(false)}
                className="bg-red-500 text-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Content - Flex grow to fill available space */}
            <div className="flex-1 flex flex-col p-6">
              <div className="text-center mb-6">
                <h1 className="text-4xl lg:text-5xl font-black text-black mb-3 transform -rotate-2">
                  WELCOME TO DISHSYNC!
                </h1>
                <h2 className="text-xl lg:text-2xl font-bold text-black transform rotate-1">
                  Your Ultimate Cooking Companion
                </h2>
              </div>

              <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 items-center">
                {/* Image */}
                <div className="flex justify-center">
                  <div className="bg-white border-4 border-black p-4 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3">
                    <Image
                      src="/assets/chef.png"
                      alt="Chef illustration"
                      width={280}
                      height={280}
                      className="rounded-lg"
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="space-y-4">
                  <div className="bg-white border-4 border-black p-4 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
                    <p className="text-lg font-bold text-black leading-relaxed">
                      DishSync is the REVOLUTIONARY multi-dish cooking assistant that will transform your kitchen experience! Say goodbye to burnt food and timing disasters!
                    </p>
                  </div>

                  <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                    <h3 className="text-white font-black text-lg mb-2">HOW TO GET STARTED:</h3>
                    <ul className="text-white font-bold space-y-1">
                      <li>1. Add your favorite RECIPES</li>
                      <li>2. Create a COOKING SESSION</li>
                      <li>3. Follow the PREP MODE</li>
                      <li>4. Switch to COOK MODE</li>
                      <li>5. Enjoy PERFECT results!</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-center p-6 border-t-4 border-black flex-shrink-0">
              <button
                onClick={() => setShowGettingStarted(false)}
                className="px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
              >
                <span className="flex items-center gap-2">START COOKING <ChefHat className="h-6 w-6" /></span>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Quick Actions - Collapsible from bottom */}
      <div className={`fixed bottom-0 right-6 z-40 transition-all duration-300 ease-in-out ${showQuickActions ? 'translate-y-0' : 'translate-y-[calc(100%-80px)]'}`}>
        <div className="bg-black border-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] rounded-t-2xl overflow-hidden">
          {/* Toggle Button */}
          <button
            onClick={() => setShowQuickActions(!showQuickActions)}
            className="w-full p-4 bg-purple-500 text-white font-black text-lg border-b-4 border-white hover:bg-purple-600 transition-colors flex items-center justify-center space-x-2"
          >
            <span className="flex items-center gap-2">QUICK ACTIONS <Zap className="h-5 w-5" /></span>
            <ChevronUp className={`h-6 w-6 transform transition-transform ${showQuickActions ? 'rotate-180' : ''}`} />
          </button>

          {/* Quick Actions Content */}
          <div className="p-6 space-y-4 w-80">
            {/* Add Recipe */}
            <div className="flex items-center space-x-4 p-4 bg-yellow-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:rotate-1">
              <div className="w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0">
                <Plus className="h-6 w-6 text-black" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-black text-black text-lg leading-tight flex items-center gap-2">ADD RECIPE <Plus className="h-5 w-5" /></h3>
                <p className="text-black font-bold text-sm leading-tight">Create a new recipe</p>
              </div>
            </div>

            {/* Start Session */}
            <div className="flex items-center space-x-4 p-4 bg-green-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:-rotate-1">
              <div className="w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0">
                <Clock className="h-6 w-6 text-black" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-black text-black text-lg leading-tight flex items-center gap-2">START SESSION <Zap className="h-5 w-5" /></h3>
                <p className="text-black font-bold text-sm leading-tight">Begin cooking session</p>
              </div>
            </div>

            {/* View Dashboard */}
            <div className="flex items-center space-x-4 p-4 bg-blue-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:rotate-1">
              <div className="w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0">
                <ChefHat className="h-6 w-6 text-black" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-black text-black text-lg leading-tight flex items-center gap-2">DASHBOARD <BarChart3 className="h-5 w-5" /></h3>
                <p className="text-black font-bold text-sm leading-tight">View cooking status</p>
              </div>
            </div>

            {/* Help */}
            <div
              onClick={() => setShowGettingStarted(true)}
              className="flex items-center space-x-4 p-4 bg-red-400 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all cursor-pointer transform hover:-rotate-1"
            >
              <div className="w-12 h-12 bg-white border-4 border-black rounded-full flex items-center justify-center shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] flex-shrink-0">
                <Sparkles className="h-6 w-6 text-black" />
              </div>
              <div className="flex-1 min-w-0">
                <h3 className="font-black text-black text-lg leading-tight flex items-center gap-2">HELP <HelpCircle className="h-5 w-5" /></h3>
                <p className="text-black font-bold text-sm leading-tight">Getting started guide</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

function DashboardView() {
  return (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="relative">
        <div className="text-center mb-8">
          <h1 className="text-5xl lg:text-6xl font-black text-black mb-3 tracking-tight transform -rotate-2">
            DISHSYNC
          </h1>
          <div className="bg-black border-4 border-white p-4 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto">
            <p className="text-lg lg:text-xl font-bold text-white leading-tight">
              The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency!
            </p>
            <div className="flex justify-center items-center mt-3 space-x-3">
              <Sparkles className="h-6 w-6 text-yellow-400 animate-pulse" />
              <span className="text-yellow-400 font-black text-lg">COOK LIKE A PRO!</span>
              <Sparkles className="h-6 w-6 text-yellow-400 animate-pulse" />
            </div>
          </div>
        </div>

        {/* Status Cards Row */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
          <div className="bg-red-500 border-4 border-black p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform rotate-1 hover:rotate-3 transition-transform">
            <div className="flex flex-col items-center justify-center h-full min-h-[80px]">
              <div className="text-3xl font-black text-white mb-2">0</div>
              <div className="text-white font-bold text-xs text-center leading-tight">ACTIVE SESSIONS</div>
            </div>
          </div>
          <div className="bg-blue-500 border-4 border-black p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform -rotate-1 hover:-rotate-3 transition-transform">
            <div className="flex flex-col items-center justify-center h-full min-h-[80px]">
              <div className="text-3xl font-black text-white mb-2">0</div>
              <div className="text-white font-bold text-xs text-center leading-tight">SAVED RECIPES</div>
            </div>
          </div>
          <div className="bg-green-500 border-4 border-black p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform rotate-1 hover:rotate-3 transition-transform">
            <div className="flex flex-col items-center justify-center h-full min-h-[80px]">
              <div className="text-3xl font-black text-white mb-2">0</div>
              <div className="text-white font-bold text-xs text-center leading-tight">COMPLETED MEALS</div>
            </div>
          </div>
          <div className="bg-purple-500 border-4 border-black p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform -rotate-1 hover:-rotate-3 transition-transform">
            <div className="flex flex-col items-center justify-center h-full min-h-[80px]">
              <div className="text-3xl font-black text-white mb-2">0</div>
              <div className="text-white font-bold text-xs text-center leading-tight">TIME SAVED</div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Features Section */}
      <div>
        <h2 className="text-3xl font-black text-black mb-6 text-center transform rotate-1">
          POWERFUL FEATURES!
        </h2>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Timer Sync Feature */}
          <div className="bg-gradient-to-br from-blue-400 to-blue-500 border-4 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="flex items-start space-x-4 mb-6">
              <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform rotate-12 flex-shrink-0">
                <Clock className="h-8 w-8 text-black" />
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-black text-black mb-2 leading-tight">TIMER SYNC</h3>
                <p className="text-black font-bold text-sm leading-tight">Perfect Timing, Every Time</p>
              </div>
            </div>
            <p className="text-black font-bold mb-6 text-base leading-relaxed">
              Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!
            </p>
            <div className="flex justify-start">
              <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-base transform hover:scale-105">
                START SESSION →
              </button>
            </div>
          </div>

          {/* Prep & Cook Feature */}
          <div className="bg-gradient-to-br from-green-400 to-green-500 border-4 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="flex items-start space-x-4 mb-6">
              <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform -rotate-12 flex-shrink-0">
                <ChefHat className="h-8 w-8 text-black" />
              </div>
              <div className="flex-1">
                <h3 className="text-2xl font-black text-black mb-2 leading-tight">PREP & COOK</h3>
                <p className="text-black font-bold text-sm leading-tight">Smart Workflow Management</p>
              </div>
            </div>
            <p className="text-black font-bold mb-6 text-base leading-relaxed">
              Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!
            </p>
            <div className="flex justify-start">
              <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-base transform hover:scale-105">
                MANAGE RECIPES →
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Secondary Features */}
      <div>
        <h2 className="text-2xl font-black text-black mb-4 text-center transform -rotate-1">
          BONUS FEATURES!
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="bg-purple-400 border-4 border-black p-5 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all">
            <div className="flex items-start space-x-3 mb-4">
              <div className="bg-white border-4 border-black p-2 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] flex-shrink-0">
                <Users className="h-6 w-6 text-black" />
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-black text-black leading-tight">SMART LISTS</h3>
              </div>
            </div>
            <p className="text-black font-bold mb-4 text-sm leading-relaxed">
              Automatically combine ingredient lists and generate SMART shopping lists!
            </p>
            <div className="flex justify-start">
              <button className="bg-black text-white font-black px-4 py-2 border-4 border-white shadow-[3px_3px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-sm">
                VIEW LISTS →
              </button>
            </div>
          </div>

          <div className="bg-yellow-400 border-4 border-black p-5 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all">
            <div className="flex items-start space-x-3 mb-4">
              <div className="bg-white border-4 border-black p-2 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)] flex-shrink-0">
                <Target className="h-6 w-6 text-black" />
              </div>
              <div className="flex-1">
                <h3 className="text-xl font-black text-black leading-tight">MEAL PLANNING</h3>
              </div>
            </div>
            <p className="text-black font-bold mb-4 text-sm leading-relaxed">
              Plan entire meals with multiple courses and get a MASTER timeline!
            </p>
            <div className="flex justify-start">
              <button className="bg-black text-white font-black px-4 py-2 border-4 border-white shadow-[3px_3px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors text-sm">
                PLAN MEALS →
              </button>
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}

function RecipesView() {
  // Mock data for demonstration - in real app this would come from state/API
  const hasRecipes = false; // Change to true to see recipes layout
  const recipeCategories = [
    { name: "MAIN DISHES", count: 0, color: "bg-red-500" },
    { name: "APPETIZERS", count: 0, color: "bg-blue-500" },
    { name: "DESSERTS", count: 0, color: "bg-green-500" },
    { name: "SIDES", count: 0, color: "bg-purple-500" }
  ];

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="relative">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6">
          <div>
            <h1 className="text-4xl lg:text-5xl font-black text-black transform -rotate-2 mb-2">
              MY RECIPES
            </h1>
            <p className="text-lg font-bold text-black/80">
              Organize and manage your culinary creations
            </p>
          </div>
          <button className="px-6 py-3 bg-orange-500 text-white font-black text-lg border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform rotate-2 self-start lg:self-center">
            <span className="flex items-center gap-2">ADD RECIPE <Plus className="h-5 w-5" /></span>
          </button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {recipeCategories.map((category, index) => (
          <div key={category.name} className={`${category.color} border-4 border-black p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform ${index % 2 === 0 ? 'rotate-1' : '-rotate-1'} hover:rotate-3 transition-transform cursor-pointer`}>
            <div className="flex flex-col items-center justify-center h-full min-h-[80px]">
              <div className="text-3xl font-black text-white mb-2">{category.count}</div>
              <div className="text-white font-bold text-xs text-center leading-tight">{category.name}</div>
            </div>
          </div>
        ))}
      </div>

      {/* Search and Filter Bar */}
      <div className="bg-black border-4 border-white p-6 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform rotate-1">
        <div className="flex flex-col md:flex-row gap-4 items-stretch md:items-center">
          <div className="flex-1">
            <div className="bg-white border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] h-full flex items-center">
              <input
                type="text"
                placeholder="SEARCH RECIPES..."
                className="w-full font-bold text-black placeholder-black/60 bg-transparent outline-none text-base"
              />
            </div>
          </div>
          <div className="flex gap-3 flex-shrink-0">
            <button className="px-6 py-4 bg-yellow-400 text-black font-black border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all text-base">
              FILTER
            </button>
            <button className="px-6 py-4 bg-blue-400 text-black font-black border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all text-base">
              SORT
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      {!hasRecipes ? (
        /* Empty State */
        <div className="bg-gradient-to-br from-yellow-300 to-yellow-400 border-6 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
          <div className="max-w-2xl mx-auto">
            <div className="bg-white border-4 border-black p-8 inline-block shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-8">
              <ChefHat className="h-20 w-20 text-black" />
            </div>
            <h2 className="text-4xl font-black text-black mb-4 transform rotate-2">
              NO RECIPES YET!
            </h2>
            <p className="text-xl font-bold text-black mb-8 leading-relaxed">
              Your recipe collection is waiting to be filled! Start by adding your FIRST recipe
              and unlock the power of coordinated cooking.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform hover:scale-105">
                <span className="flex items-center gap-2">ADD YOUR FIRST RECIPE <Plus className="h-6 w-6" /></span>
              </button>
              <button className="px-8 py-4 bg-white text-black font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform hover:scale-105">
                IMPORT RECIPES
              </button>
            </div>
          </div>
        </div>
      ) : (
        /* Recipes Grid - This would show when hasRecipes is true */
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Recipe cards would go here */}
          <div className="bg-white border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
            <div className="bg-red-400 border-4 border-black p-4 mb-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <h3 className="font-black text-black text-lg">SAMPLE RECIPE</h3>
            </div>
            <p className="font-bold text-black mb-4">A delicious sample recipe description...</p>
            <div className="flex justify-between items-center">
              <span className="text-sm font-bold text-black/70">30 mins</span>
              <button className="px-4 py-2 bg-green-500 text-white font-black border-4 border-black shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]">
                COOK!
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function SessionsView() {
  // Mock data for demonstration - in real app this would come from state/API
  const hasSessions = false; // Change to true to see sessions layout
  const sessionStats = [
    { name: "ACTIVE", count: 0, color: "bg-green-500", icon: Clock },
    { name: "COMPLETED", count: 0, color: "bg-blue-500", icon: ChefHat },
    { name: "PLANNED", count: 0, color: "bg-yellow-500", icon: Target },
    { name: "TOTAL TIME SAVED", count: "0h", color: "bg-purple-500", icon: Zap }
  ];

  return (
    <div className="space-y-8">
      {/* Header Section */}
      <div className="relative">
        <div className="flex flex-col lg:flex-row lg:justify-between lg:items-center gap-6">
          <div>
            <h1 className="text-4xl lg:text-5xl font-black text-black transform rotate-2 mb-2">
              COOKING SESSIONS
            </h1>
            <p className="text-lg font-bold text-black/80">
              Coordinate multiple dishes with perfect timing
            </p>
          </div>
          <button className="px-6 py-3 bg-blue-500 text-white font-black text-lg border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform -rotate-2 self-start lg:self-center">
            <span className="flex items-center gap-2">NEW SESSION <Zap className="h-5 w-5" /></span>
          </button>
        </div>
      </div>

      {/* Session Stats */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4">
        {sessionStats.map((stat, index) => {
          const IconComponent = stat.icon;
          return (
            <div key={stat.name} className={`${stat.color} border-4 border-black p-5 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform ${index % 2 === 0 ? 'rotate-1' : '-rotate-1'} hover:rotate-3 transition-transform cursor-pointer`}>
              <div className="flex flex-col items-center justify-center h-full min-h-[100px]">
                <IconComponent className="h-8 w-8 text-white mb-3" />
                <div className="text-3xl font-black text-white mb-2">{stat.count}</div>
                <div className="text-white font-bold text-xs text-center leading-tight">{stat.name}</div>
              </div>
            </div>
          );
        })}
      </div>

      {/* Session Controls */}
      <div className="bg-black border-4 border-white p-6 shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] transform -rotate-1">
        <div className="flex flex-col md:flex-row gap-6 items-start md:items-center">
          <div className="flex-1">
            <h3 className="text-white font-black text-xl mb-2 leading-tight">QUICK ACTIONS</h3>
            <p className="text-white/80 font-bold text-base leading-relaxed">Manage your cooking sessions efficiently</p>
          </div>
          <div className="flex flex-col sm:flex-row gap-3 w-full md:w-auto">
            <button className="flex items-center justify-center px-6 py-4 bg-green-400 text-black font-black border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all text-base">
              <span className="flex items-center gap-2">ACTIVE TIMERS <Clock className="h-5 w-5" /></span>
            </button>
            <button className="flex items-center justify-center px-6 py-4 bg-yellow-400 text-black font-black border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all text-base">
              <span className="flex items-center gap-2">SCHEDULE <Target className="h-5 w-5" /></span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      {!hasSessions ? (
        /* Empty State */
        <div className="bg-gradient-to-br from-blue-300 to-blue-400 border-6 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
          <div className="max-w-2xl mx-auto">
            <div className="bg-white border-4 border-black p-8 inline-block shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-8">
              <Clock className="h-20 w-20 text-black" />
            </div>
            <h2 className="text-4xl font-black text-black mb-4 transform -rotate-2">
              NO SESSIONS YET!
            </h2>
            <p className="text-xl font-bold text-black mb-8 leading-relaxed">
              Create your first cooking session to coordinate MULTIPLE dishes with PERFECT timing!
              Never serve cold food or burn another dish again.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform hover:scale-105">
                <span className="flex items-center gap-2">START YOUR FIRST SESSION <Zap className="h-6 w-6" /></span>
              </button>
              <button className="px-8 py-4 bg-white text-black font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform hover:scale-105">
                LEARN HOW IT WORKS
              </button>
            </div>
          </div>
        </div>
      ) : (
        /* Sessions List - This would show when hasSessions is true */
        <div className="space-y-6">
          {/* Active Sessions */}
          <div>
            <h3 className="text-2xl font-black text-black mb-4 transform rotate-1">ACTIVE SESSIONS</h3>
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-green-400 border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
                <div className="flex justify-between items-start mb-4">
                  <h4 className="font-black text-black text-lg">DINNER PARTY</h4>
                  <span className="bg-white border-2 border-black px-3 py-1 font-black text-sm">COOKING</span>
                </div>
                <p className="font-bold text-black mb-4">3 dishes • 45 mins remaining</p>
                <div className="flex justify-between items-center">
                  <div className="text-sm font-bold text-black/70">Started 15 mins ago</div>
                  <button className="px-4 py-2 bg-black text-white font-black border-4 border-white shadow-[3px_3px_0px_0px_rgba(255,255,255,1)]">
                    CONTINUE
                  </button>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Sessions */}
          <div>
            <h3 className="text-2xl font-black text-black mb-4 transform -rotate-1">RECENT SESSIONS</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div className="bg-white border-4 border-black p-4 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all">
                <div className="bg-blue-400 border-4 border-black p-3 mb-3 shadow-[3px_3px_0px_0px_rgba(0,0,0,1)]">
                  <h4 className="font-black text-black">WEEKEND BRUNCH</h4>
                </div>
                <p className="font-bold text-black mb-3 text-sm">Completed • 2 dishes • 1h 30m</p>
                <button className="px-3 py-2 bg-purple-500 text-white font-black text-sm border-4 border-black shadow-[2px_2px_0px_0px_rgba(0,0,0,1)]">
                  REPEAT
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
