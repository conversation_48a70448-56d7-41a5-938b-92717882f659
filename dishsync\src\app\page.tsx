'use client';

import { useState } from 'react';
import { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';
// import GettingStartedModal from '@/components/GettingStartedModal';

export default function Home() {
  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');
  const [showGettingStarted, setShowGettingStarted] = useState(false);

  return (
    <div className="min-h-screen bg-yellow-300">
      {/* Header */}
      <header className="bg-black border-b-4 border-white shadow-[8px_8px_0px_0px_rgba(255,255,255,1)]">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <Image
                src="/assets/logo.png"
                alt="DishSync Logo"
                width={48}
                height={48}
                className="rounded-lg border-2 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]"
              />
              <h1 className="text-3xl font-black text-white tracking-tight">DISHSYNC</h1>
            </div>
            <nav className="flex space-x-4">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  activeTab === 'dashboard'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'
                }`}
              >
                DASHBOARD
              </button>
              <button
                onClick={() => setActiveTab('recipes')}
                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  activeTab === 'recipes'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'
                }`}
              >
                RECIPES
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`px-6 py-3 font-black text-lg border-4 border-white transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  activeTab === 'sessions'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400'
                }`}
              >
                SESSIONS
              </button>
              <button
                onClick={() => setShowGettingStarted(true)}
                className="px-6 py-3 font-black text-lg border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:translate-x-1 hover:translate-y-1 hover:shadow-none hover:bg-green-600"
              >
                HELP
              </button>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {activeTab === 'dashboard' && <DashboardView />}
        {activeTab === 'recipes' && <RecipesView />}
        {activeTab === 'sessions' && <SessionsView />}
      </main>

      {/* Getting Started Modal */}
      {/* <GettingStartedModal
        isOpen={showGettingStarted}
        onClose={() => setShowGettingStarted(false)}
      /> */}
    </div>
  );
}

function DashboardView() {
  return (
    <div className="space-y-12">
      {/* Welcome Section */}
      <div className="text-center">
        <h2 className="text-6xl font-black text-black mb-6 tracking-tight transform -rotate-1">
          WELCOME TO DISHSYNC!
        </h2>
        <div className="bg-black border-4 border-white p-8 shadow-[12px_12px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto">
          <p className="text-2xl font-bold text-white leading-relaxed">
            The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency.
            Never worry about timing multiple dishes again!
          </p>
          <div className="flex justify-center mt-6">
            <Sparkles className="h-8 w-8 text-yellow-400 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="bg-blue-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <Clock className="h-8 w-8 text-black" />
            </div>
            <h3 className="text-2xl font-black text-black">TIMER SYNC</h3>
          </div>
          <p className="text-black font-bold mb-6 text-lg leading-tight">
            Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!
          </p>
          <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors">
            START SESSION →
          </button>
        </div>

        <div className="bg-green-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <ChefHat className="h-8 w-8 text-black" />
            </div>
            <h3 className="text-2xl font-black text-black">PREP & COOK</h3>
          </div>
          <p className="text-black font-bold mb-6 text-lg leading-tight">
            Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!
          </p>
          <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors">
            MANAGE RECIPES →
          </button>
        </div>

        <div className="bg-purple-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <Users className="h-8 w-8 text-black" />
            </div>
            <h3 className="text-2xl font-black text-black">SMART LISTS</h3>
          </div>
          <p className="text-black font-bold mb-6 text-lg leading-tight">
            Automatically combine ingredient lists and generate SMART shopping lists!
          </p>
          <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors">
            VIEW LISTS →
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-red-500 border-4 border-black p-8 shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1">
        <h3 className="text-3xl font-black text-white mb-8 text-center transform rotate-1">QUICK ACTIONS!</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <button className="flex items-center justify-center px-6 py-4 bg-yellow-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3">
            <Plus className="mr-2 h-5 w-5" />
            ADD RECIPE
          </button>
          <button className="flex items-center justify-center px-6 py-4 bg-blue-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3">
            <Clock className="mr-2 h-5 w-5" />
            NEW SESSION
          </button>
          <button className="flex items-center justify-center px-6 py-4 bg-green-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3">
            <ChefHat className="mr-2 h-5 w-5" />
            BROWSE RECIPES
          </button>
          <button className="flex items-center justify-center px-6 py-4 bg-purple-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3">
            <Target className="mr-2 h-5 w-5" />
            DASHBOARD
          </button>
        </div>
      </div>
    </div>
  );
}

function RecipesView() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-5xl font-black text-black transform -rotate-2">MY RECIPES</h2>
        <button className="px-8 py-4 bg-orange-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform rotate-2">
          <Plus className="mr-2 h-6 w-6" />
          ADD RECIPE!
        </button>
      </div>

      <div className="bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
        <div className="bg-yellow-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-6">
          <ChefHat className="h-16 w-16 text-black" />
        </div>
        <h3 className="text-3xl font-black text-black mb-4">NO RECIPES YET!</h3>
        <p className="text-xl font-bold text-black mb-8 max-w-md mx-auto">
          Start by adding your FIRST recipe to begin coordinating your cooking like a PRO!
        </p>
        <button className="px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <Plus className="mr-2 h-6 w-6" />
          ADD YOUR FIRST RECIPE!
        </button>
      </div>
    </div>
  );
}

function SessionsView() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-5xl font-black text-black transform rotate-2">COOKING SESSIONS</h2>
        <button className="px-8 py-4 bg-blue-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform -rotate-2">
          <Zap className="mr-2 h-6 w-6" />
          NEW SESSION!
        </button>
      </div>

      <div className="bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1">
        <div className="bg-blue-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-3 mb-6">
          <Clock className="h-16 w-16 text-black" />
        </div>
        <h3 className="text-3xl font-black text-black mb-4">NO SESSIONS YET!</h3>
        <p className="text-xl font-bold text-black mb-8 max-w-md mx-auto">
          Create a cooking session to coordinate MULTIPLE dishes with PERFECT timing!
        </p>
        <button className="px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <Zap className="mr-2 h-6 w-6" />
          START YOUR FIRST SESSION!
        </button>
      </div>
    </div>
  );
}
