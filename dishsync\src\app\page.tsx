'use client';

import { useState } from 'react';
import { ChefHat, Clock, Users, Plus, Zap, Target, Sparkles, X } from 'lucide-react';
import Link from 'next/link';
import Image from 'next/image';


export default function Home() {
  const [activeTab, setActiveTab] = useState<'recipes' | 'sessions' | 'dashboard'>('dashboard');
  const [showGettingStarted, setShowGettingStarted] = useState(false);

  return (
    <div className="min-h-screen bg-yellow-300">
      {/* Header */}
      <header className="bg-black border-b-8 border-yellow-400 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full bg-gradient-to-r from-red-500 via-yellow-400 to-blue-500"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            {/* Logo Section */}
            <div className="flex items-center space-x-6">
              <div className="relative">
                <div className="bg-yellow-400 border-4 border-white p-3 shadow-[6px_6px_0px_0px_rgba(255,255,255,1)] transform rotate-3 hover:rotate-6 transition-transform">
                  <Image
                    src="/assets/logo.png"
                    alt="DishSync Logo"
                    width={56}
                    height={56}
                    className="rounded-lg"
                  />
                </div>
              </div>
              <div className="flex flex-col">
                <h1 className="text-4xl lg:text-5xl font-black text-white tracking-tight transform -rotate-1 hover:rotate-0 transition-transform">
                  DISHSYNC
                </h1>
                <p className="text-yellow-400 font-bold text-sm lg:text-base transform rotate-1 mt-1">
                  COOK LIKE A PRO!
                </p>
              </div>
            </div>

            {/* Navigation */}
            <nav className="flex items-center space-x-3">
              <button
                onClick={() => setActiveTab('dashboard')}
                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${
                  activeTab === 'dashboard'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-yellow-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'
                }`}
              >
                DASHBOARD
              </button>
              <button
                onClick={() => setActiveTab('recipes')}
                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:-rotate-1 ${
                  activeTab === 'recipes'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] -rotate-1'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-blue-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'
                }`}
              >
                RECIPES
              </button>
              <button
                onClick={() => setActiveTab('sessions')}
                className={`px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white transition-all transform hover:scale-105 hover:rotate-1 ${
                  activeTab === 'sessions'
                    ? 'bg-red-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] rotate-1'
                    : 'bg-white text-black shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-purple-400 hover:shadow-[6px_6px_0px_0px_rgba(255,255,255,1)]'
                }`}
              >
                SESSIONS
              </button>

              {/* Help Button - Special Styling */}
              <div className="relative ml-2">
                <button
                  onClick={() => setShowGettingStarted(true)}
                  className="px-4 lg:px-6 py-3 font-black text-sm lg:text-base border-4 border-white bg-green-500 text-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] transition-all transform hover:scale-110 hover:rotate-3 hover:bg-green-400 hover:shadow-[8px_8px_0px_0px_rgba(255,255,255,1)] relative overflow-hidden"
                >
                  <span className="relative z-10">HELP</span>
                  <div className="absolute inset-0 bg-gradient-to-r from-green-400 to-green-600 opacity-0 hover:opacity-100 transition-opacity"></div>
                </button>
                {/* Pulsing indicator */}
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-yellow-400 border-2 border-white rounded-full animate-pulse"></div>
              </div>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {activeTab === 'dashboard' && <DashboardView />}
        {activeTab === 'recipes' && <RecipesView />}
        {activeTab === 'sessions' && <SessionsView />}
      </main>

      {/* Getting Started Modal */}
      {showGettingStarted && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-yellow-400 border-8 border-black shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] max-w-4xl w-full max-h-[90vh] overflow-y-auto transform rotate-1">
            {/* Header */}
            <div className="flex justify-between items-center p-6 border-b-4 border-black">
              <div className="flex items-center space-x-4">
                <div className="bg-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
                  <Image
                    src="/assets/logo.png"
                    alt="DishSync Logo"
                    width={32}
                    height={32}
                  />
                </div>
                <h2 className="text-2xl font-black text-black">
                  GETTING STARTED GUIDE
                </h2>
              </div>
              <button
                onClick={() => setShowGettingStarted(false)}
                className="bg-red-500 text-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Content */}
            <div className="p-8">
              <div className="text-center mb-8">
                <h1 className="text-5xl font-black text-black mb-4 transform -rotate-2">
                  WELCOME TO DISHSYNC!
                </h1>
                <h2 className="text-2xl font-bold text-black transform rotate-1">
                  Your Ultimate Cooking Companion
                </h2>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                {/* Image */}
                <div className="flex justify-center">
                  <div className="bg-white border-4 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3">
                    <Image
                      src="/assets/chef.png"
                      alt="Chef illustration"
                      width={300}
                      height={300}
                      className="rounded-lg"
                    />
                  </div>
                </div>

                {/* Content */}
                <div className="space-y-6">
                  <div className="bg-white border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
                    <p className="text-xl font-bold text-black leading-relaxed">
                      DishSync is the REVOLUTIONARY multi-dish cooking assistant that will transform your kitchen experience! Say goodbye to burnt food and timing disasters!
                    </p>
                  </div>

                  <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                    <h3 className="text-white font-black text-lg mb-2">HOW TO GET STARTED:</h3>
                    <ul className="text-white font-bold space-y-1">
                      <li>1. Add your favorite RECIPES</li>
                      <li>2. Create a COOKING SESSION</li>
                      <li>3. Follow the PREP MODE</li>
                      <li>4. Switch to COOK MODE</li>
                      <li>5. Enjoy PERFECT results!</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="flex justify-center p-6 border-t-4 border-black">
              <button
                onClick={() => setShowGettingStarted(false)}
                className="px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
              >
                START COOKING! 🍳
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

function DashboardView() {
  return (
    <div className="space-y-12">
      {/* Welcome Section */}
      <div className="text-center">
        <h2 className="text-6xl font-black text-black mb-6 tracking-tight transform -rotate-1">
          WELCOME TO DISHSYNC!
        </h2>
        <div className="bg-black border-4 border-white p-8 shadow-[12px_12px_0px_0px_rgba(255,255,255,1)] transform rotate-1 max-w-4xl mx-auto">
          <p className="text-2xl font-bold text-white leading-relaxed">
            The ULTIMATE multi-dish cooking assistant that coordinates your recipes for MAXIMUM efficiency.
            Never worry about timing multiple dishes again!
          </p>
          <div className="flex justify-center mt-6">
            <Sparkles className="h-8 w-8 text-yellow-400 animate-pulse" />
          </div>
        </div>
      </div>

      {/* Feature Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="bg-blue-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <Clock className="h-8 w-8 text-black" />
            </div>
            <h3 className="text-2xl font-black text-black">TIMER SYNC</h3>
          </div>
          <p className="text-black font-bold mb-6 text-lg leading-tight">
            Input ALL your dishes and get a coordinated timeline showing EXACTLY when to start each step!
          </p>
          <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors">
            START SESSION →
          </button>
        </div>

        <div className="bg-green-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <ChefHat className="h-8 w-8 text-black" />
            </div>
            <h3 className="text-2xl font-black text-black">PREP & COOK</h3>
          </div>
          <p className="text-black font-bold mb-6 text-lg leading-tight">
            Consolidate overlapping prep steps and get GUIDED cooking with real-time prompts!
          </p>
          <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors">
            MANAGE RECIPES →
          </button>
        </div>

        <div className="bg-purple-400 border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <div className="flex items-center space-x-4 mb-6">
            <div className="bg-white border-4 border-black p-3 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <Users className="h-8 w-8 text-black" />
            </div>
            <h3 className="text-2xl font-black text-black">SMART LISTS</h3>
          </div>
          <p className="text-black font-bold mb-6 text-lg leading-tight">
            Automatically combine ingredient lists and generate SMART shopping lists!
          </p>
          <button className="bg-black text-white font-black px-6 py-3 border-4 border-white shadow-[4px_4px_0px_0px_rgba(255,255,255,1)] hover:bg-red-500 transition-colors">
            VIEW LISTS →
          </button>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-red-500 border-4 border-black p-8 shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1">
        <h3 className="text-3xl font-black text-white mb-8 text-center transform rotate-1">QUICK ACTIONS!</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
          <button className="flex items-center justify-center px-6 py-4 bg-yellow-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3">
            <Plus className="mr-2 h-5 w-5" />
            ADD RECIPE
          </button>
          <button className="flex items-center justify-center px-6 py-4 bg-blue-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3">
            <Clock className="mr-2 h-5 w-5" />
            NEW SESSION
          </button>
          <button className="flex items-center justify-center px-6 py-4 bg-green-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:rotate-3">
            <ChefHat className="mr-2 h-5 w-5" />
            BROWSE RECIPES
          </button>
          <button className="flex items-center justify-center px-6 py-4 bg-purple-400 text-black font-black border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all transform hover:-rotate-3">
            <Target className="mr-2 h-5 w-5" />
            DASHBOARD
          </button>
        </div>
      </div>
    </div>
  );
}

function RecipesView() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-5xl font-black text-black transform -rotate-2">MY RECIPES</h2>
        <button className="px-8 py-4 bg-orange-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform rotate-2">
          <Plus className="mr-2 h-6 w-6" />
          ADD RECIPE!
        </button>
      </div>

      <div className="bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
        <div className="bg-yellow-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform -rotate-3 mb-6">
          <ChefHat className="h-16 w-16 text-black" />
        </div>
        <h3 className="text-3xl font-black text-black mb-4">NO RECIPES YET!</h3>
        <p className="text-xl font-bold text-black mb-8 max-w-md mx-auto">
          Start by adding your FIRST recipe to begin coordinating your cooking like a PRO!
        </p>
        <button className="px-8 py-4 bg-red-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <Plus className="mr-2 h-6 w-6" />
          ADD YOUR FIRST RECIPE!
        </button>
      </div>
    </div>
  );
}

function SessionsView() {
  return (
    <div className="space-y-8">
      <div className="flex justify-between items-center">
        <h2 className="text-5xl font-black text-black transform rotate-2">COOKING SESSIONS</h2>
        <button className="px-8 py-4 bg-blue-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all transform -rotate-2">
          <Zap className="mr-2 h-6 w-6" />
          NEW SESSION!
        </button>
      </div>

      <div className="bg-white border-4 border-black p-12 text-center shadow-[12px_12px_0px_0px_rgba(0,0,0,1)] transform -rotate-1">
        <div className="bg-blue-400 border-4 border-black p-6 inline-block shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-3 mb-6">
          <Clock className="h-16 w-16 text-black" />
        </div>
        <h3 className="text-3xl font-black text-black mb-4">NO SESSIONS YET!</h3>
        <p className="text-xl font-bold text-black mb-8 max-w-md mx-auto">
          Create a cooking session to coordinate MULTIPLE dishes with PERFECT timing!
        </p>
        <button className="px-8 py-4 bg-green-500 text-white font-black text-xl border-4 border-black shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all">
          <Zap className="mr-2 h-6 w-6" />
          START YOUR FIRST SESSION!
        </button>
      </div>
    </div>
  );
}
