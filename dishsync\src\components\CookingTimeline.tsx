'use client';

import { useState, useEffect } from 'react';
import { Clock, ChefHat, Play, CheckCircle, AlertCircle } from 'lucide-react';
import type { CookingTimeline, SessionStep } from '@/types';

interface CookingTimelineProps {
  timeline: CookingTimeline;
  currentTime?: Date;
  onStepClick?: (step: SessionStep) => void;
  className?: string;
}

export default function CookingTimelineComponent({
  timeline,
  currentTime = new Date(),
  onStepClick,
  className = ''
}: CookingTimelineProps) {
  const [viewMode, setViewMode] = useState<'timeline' | 'gantt'>('timeline');

  // Calculate timeline bounds
  const startTimes = timeline.steps.map(step => new Date(step.scheduled_start_time));
  const endTimes = timeline.steps.map(step => new Date(step.scheduled_end_time));
  
  const timelineStart = new Date(Math.min(...startTimes.map(d => d.getTime())));
  const timelineEnd = new Date(Math.max(...endTimes.map(d => d.getTime())));
  const totalDuration = timelineEnd.getTime() - timelineStart.getTime();

  const getStepPosition = (step: SessionStep) => {
    const stepStart = new Date(step.scheduled_start_time);
    const stepEnd = new Date(step.scheduled_end_time);
    
    const startOffset = stepStart.getTime() - timelineStart.getTime();
    const duration = stepEnd.getTime() - stepStart.getTime();
    
    const leftPercent = (startOffset / totalDuration) * 100;
    const widthPercent = (duration / totalDuration) * 100;
    
    return { left: leftPercent, width: widthPercent };
  };

  const getCurrentTimePosition = () => {
    const currentOffset = currentTime.getTime() - timelineStart.getTime();
    return Math.max(0, Math.min(100, (currentOffset / totalDuration) * 100));
  };

  const getStepColor = (step: SessionStep): string => {
    const now = currentTime;
    const startTime = new Date(step.scheduled_start_time);
    const endTime = new Date(step.scheduled_end_time);

    if (step.status === 'completed') return 'bg-green-500';
    if (step.status === 'in_progress') return 'bg-blue-500';
    if (now > endTime && step.status !== 'completed') return 'bg-red-500';
    if (now >= startTime && now <= endTime) return 'bg-yellow-500';
    if (step.step?.is_prep_step) return 'bg-purple-400';
    return 'bg-gray-400';
  };

  const getStepTextColor = (step: SessionStep): string => {
    const bgColor = getStepColor(step);
    return bgColor.includes('yellow') ? 'text-gray-900' : 'text-white';
  };

  // Group steps by recipe for better visualization
  const stepsByRecipe = timeline.steps.reduce((groups, step) => {
    const recipeId = step.recipe_id;
    if (!groups[recipeId]) {
      groups[recipeId] = [];
    }
    groups[recipeId].push(step);
    return groups;
  }, {} as Record<string, SessionStep[]>);

  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const formatDuration = (minutes: number): string => {
    if (minutes < 60) return `${minutes}m`;
    const hours = Math.floor(minutes / 60);
    const remainingMinutes = minutes % 60;
    return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
  };

  return (
    <div className={`bg-white rounded-lg shadow-md ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900 flex items-center">
            <Clock className="mr-2 h-5 w-5 text-blue-600" />
            Cooking Timeline
          </h2>
          <div className="flex space-x-2">
            <button
              onClick={() => setViewMode('timeline')}
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                viewMode === 'timeline'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Timeline
            </button>
            <button
              onClick={() => setViewMode('gantt')}
              className={`px-3 py-2 rounded-md text-sm font-medium ${
                viewMode === 'gantt'
                  ? 'bg-blue-100 text-blue-700'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Gantt
            </button>
          </div>
        </div>

        {/* Timeline Summary */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Start Time:</span>
            <div className="font-medium">{formatTime(timelineStart)}</div>
          </div>
          <div>
            <span className="text-gray-600">End Time:</span>
            <div className="font-medium">{formatTime(timelineEnd)}</div>
          </div>
          <div>
            <span className="text-gray-600">Total Duration:</span>
            <div className="font-medium">{formatDuration(timeline.total_duration_minutes)}</div>
          </div>
          <div>
            <span className="text-gray-600">Total Steps:</span>
            <div className="font-medium">{timeline.steps.length}</div>
          </div>
        </div>
      </div>

      {/* Legend */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center">
            <div className="w-4 h-4 bg-purple-400 rounded mr-2"></div>
            <span>Prep</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-gray-400 rounded mr-2"></div>
            <span>Upcoming</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-yellow-500 rounded mr-2"></div>
            <span>Ready</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-blue-500 rounded mr-2"></div>
            <span>In Progress</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-green-500 rounded mr-2"></div>
            <span>Completed</span>
          </div>
          <div className="flex items-center">
            <div className="w-4 h-4 bg-red-500 rounded mr-2"></div>
            <span>Overdue</span>
          </div>
        </div>
      </div>

      {/* Timeline Content */}
      <div className="p-4">
        {viewMode === 'timeline' ? (
          <TimelineView
            stepsByRecipe={stepsByRecipe}
            getStepPosition={getStepPosition}
            getCurrentTimePosition={getCurrentTimePosition}
            getStepColor={getStepColor}
            getStepTextColor={getStepTextColor}
            formatTime={formatTime}
            onStepClick={onStepClick}
          />
        ) : (
          <GanttView
            timeline={timeline}
            stepsByRecipe={stepsByRecipe}
            currentTime={currentTime}
            timelineStart={timelineStart}
            timelineEnd={timelineEnd}
            onStepClick={onStepClick}
          />
        )}
      </div>
    </div>
  );
}

interface TimelineViewProps {
  stepsByRecipe: Record<string, SessionStep[]>;
  getStepPosition: (step: SessionStep) => { left: number; width: number };
  getCurrentTimePosition: () => number;
  getStepColor: (step: SessionStep) => string;
  getStepTextColor: (step: SessionStep) => string;
  formatTime: (date: Date) => string;
  onStepClick?: (step: SessionStep) => void;
}

function TimelineView({
  stepsByRecipe,
  getStepPosition,
  getCurrentTimePosition,
  getStepColor,
  getStepTextColor,
  formatTime,
  onStepClick,
}: TimelineViewProps) {
  const currentTimePosition = getCurrentTimePosition();

  return (
    <div className="space-y-6">
      {Object.entries(stepsByRecipe).map(([recipeId, steps]) => (
        <div key={recipeId} className="space-y-2">
          <h3 className="font-medium text-gray-900 flex items-center">
            <ChefHat className="mr-2 h-4 w-4 text-orange-600" />
            {steps[0]?.recipe?.name || 'Unknown Recipe'}
          </h3>
          
          <div className="relative h-12 bg-gray-100 rounded-lg">
            {/* Current time indicator */}
            <div
              className="absolute top-0 bottom-0 w-0.5 bg-red-600 z-10"
              style={{ left: `${currentTimePosition}%` }}
            >
              <div className="absolute -top-2 -left-2 w-4 h-4 bg-red-600 rounded-full"></div>
            </div>

            {/* Steps */}
            {steps.map(step => {
              const position = getStepPosition(step);
              return (
                <div
                  key={step.id}
                  className={`absolute top-1 bottom-1 rounded cursor-pointer hover:opacity-80 transition-opacity ${getStepColor(step)} ${getStepTextColor(step)}`}
                  style={{
                    left: `${position.left}%`,
                    width: `${position.width}%`,
                  }}
                  onClick={() => onStepClick?.(step)}
                  title={`${step.step?.title || 'Step'} (${formatTime(new Date(step.scheduled_start_time))} - ${formatTime(new Date(step.scheduled_end_time))})`}
                >
                  <div className="px-2 py-1 text-xs font-medium truncate">
                    {step.step?.title || 'Step'}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      ))}
    </div>
  );
}

interface GanttViewProps {
  timeline: CookingTimeline;
  stepsByRecipe: Record<string, SessionStep[]>;
  currentTime: Date;
  timelineStart: Date;
  timelineEnd: Date;
  onStepClick?: (step: SessionStep) => void;
}

function GanttView({
  timeline,
  stepsByRecipe,
  currentTime,
  timelineStart,
  timelineEnd,
  onStepClick,
}: GanttViewProps) {
  const formatTime = (date: Date): string => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getStatusIcon = (step: SessionStep) => {
    if (step.status === 'completed') return <CheckCircle className="h-4 w-4 text-green-600" />;
    if (step.status === 'in_progress') return <Play className="h-4 w-4 text-blue-600" />;
    
    const now = currentTime;
    const endTime = new Date(step.scheduled_end_time);
    if (now > endTime) return <AlertCircle className="h-4 w-4 text-red-600" />;
    
    return <Clock className="h-4 w-4 text-gray-600" />;
  };

  return (
    <div className="space-y-4">
      {Object.entries(stepsByRecipe).map(([recipeId, steps]) => (
        <div key={recipeId} className="border border-gray-200 rounded-lg p-4">
          <h3 className="font-medium text-gray-900 mb-3 flex items-center">
            <ChefHat className="mr-2 h-4 w-4 text-orange-600" />
            {steps[0]?.recipe?.name || 'Unknown Recipe'}
          </h3>
          
          <div className="space-y-2">
            {steps
              .sort((a, b) => new Date(a.scheduled_start_time).getTime() - new Date(b.scheduled_start_time).getTime())
              .map(step => (
                <div
                  key={step.id}
                  className="flex items-center p-2 rounded-md hover:bg-gray-50 cursor-pointer"
                  onClick={() => onStepClick?.(step)}
                >
                  <div className="flex items-center space-x-3 flex-1">
                    {getStatusIcon(step)}
                    <div className="flex-1">
                      <div className="font-medium text-sm">
                        {step.step?.title || 'Step'}
                      </div>
                      <div className="text-xs text-gray-600">
                        {formatTime(new Date(step.scheduled_start_time))} - {formatTime(new Date(step.scheduled_end_time))}
                        {step.step?.duration_minutes && ` (${step.step.duration_minutes}m)`}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-500 capitalize">
                    {step.status.replace('_', ' ')}
                  </div>
                </div>
              ))}
          </div>
        </div>
      ))}
    </div>
  );
}
