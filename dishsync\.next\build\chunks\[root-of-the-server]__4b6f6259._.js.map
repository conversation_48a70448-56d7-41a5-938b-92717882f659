{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/postcss.config.mjs"], "sourcesContent": ["const config = {\n  plugins: [\"@tailwindcss/postcss\"],\n};\n\nexport default config;\n"], "names": [], "mappings": ";;;AAAA,MAAM,SAAS;IACb,SAAS;QAAC;KAAuB;AACnC;uCAEe"}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/transforms/transforms.ts"], "sourcesContent": ["/**\r\n * Shared utilities for our 2 transform implementations.\r\n */\r\n\r\nimport type { Ipc } from '../ipc/evaluate'\r\nimport { relative, isAbsolute, join, sep } from 'path'\r\nimport { type StructuredError } from '../ipc'\r\nimport { type StackFrame } from '../compiled/stacktrace-parser'\r\n\r\nexport type IpcInfoMessage =\r\n  | {\r\n      type: 'dependencies'\r\n      envVariables?: string[]\r\n      directories?: Array<[string, string]>\r\n      filePaths?: string[]\r\n      buildFilePaths?: string[]\r\n    }\r\n  | {\r\n      type: 'emittedError'\r\n      severity: 'warning' | 'error'\r\n      error: StructuredError\r\n    }\r\n  | {\r\n      type: 'log'\r\n      logs: Array<{\r\n        time: number\r\n        logType: string\r\n        args: any[]\r\n        trace?: StackFrame[]\r\n      }>\r\n    }\r\n\r\nexport type IpcRequestMessage = {\r\n  type: 'resolve'\r\n  options: any\r\n  lookupPath: string\r\n  request: string\r\n}\r\n\r\nexport type TransformIpc = Ipc<IpcInfoMessage, IpcRequestMessage>\r\n\r\nconst contextDir = process.cwd()\r\nexport const toPath = (file: string) => {\r\n  const relPath = relative(contextDir, file)\r\n  if (isAbsolute(relPath)) {\r\n    throw new Error(\r\n      `Cannot depend on path (${file}) outside of root directory (${contextDir})`\r\n    )\r\n  }\r\n  return sep !== '/' ? relPath.replaceAll(sep, '/') : relPath\r\n}\r\nexport const fromPath = (path: string) => {\r\n  return join(contextDir, sep !== '/' ? path.replaceAll('/', sep) : path)\r\n}\r\n\r\n// Patch process.env to track which env vars are read\r\nconst originalEnv = process.env\r\nconst readEnvVars = new Set<string>()\r\nprocess.env = new Proxy(originalEnv, {\r\n  get(target, prop) {\r\n    if (typeof prop === 'string') {\r\n      // We register the env var as dependency on the\r\n      // current transform and all future transforms\r\n      // since the env var might be cached in module scope\r\n      // and influence them all\r\n      readEnvVars.add(prop)\r\n    }\r\n    return Reflect.get(target, prop)\r\n  },\r\n  set(target, prop, value) {\r\n    return Reflect.set(target, prop, value)\r\n  },\r\n})\r\n\r\nexport function getReadEnvVariables(): string[] {\r\n  return Array.from(readEnvVars)\r\n}\r\n"], "names": [], "mappings": "AAAA;;CAEC;;;;;AAGD;;AAoCA,MAAM,aAAa,QAAQ,GAAG;AACvB,MAAM,SAAS,CAAC;IACrB,MAAM,UAAU,CAAA,GAAA,iGAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;IACrC,IAAI,CAAA,GAAA,iGAAA,CAAA,aAAU,AAAD,EAAE,UAAU;QACvB,MAAM,IAAI,MACR,CAAC,uBAAuB,EAAE,KAAK,6BAA6B,EAAE,WAAW,CAAC,CAAC;IAE/E;IACA,OAAO,iGAAA,CAAA,MAAG,KAAK,MAAM,QAAQ,UAAU,CAAC,iGAAA,CAAA,MAAG,EAAE,OAAO;AACtD;AACO,MAAM,WAAW,CAAC;IACvB,OAAO,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY,iGAAA,CAAA,MAAG,KAAK,MAAM,KAAK,UAAU,CAAC,KAAK,iGAAA,CAAA,MAAG,IAAI;AACpE;AAEA,qDAAqD;AACrD,MAAM,cAAc,QAAQ,GAAG;AAC/B,MAAM,cAAc,IAAI;AACxB,QAAQ,GAAG,GAAG,IAAI,MAAM,aAAa;IACnC,KAAI,MAAM,EAAE,IAAI;QACd,IAAI,OAAO,SAAS,UAAU;YAC5B,+CAA+C;YAC/C,8CAA8C;YAC9C,oDAAoD;YACpD,yBAAyB;YACzB,YAAY,GAAG,CAAC;QAClB;QACA,OAAO,QAAQ,GAAG,CAAC,QAAQ;IAC7B;IACA,KAAI,MAAM,EAAE,IAAI,EAAE,KAAK;QACrB,OAAO,QAAQ,GAAG,CAAC,QAAQ,MAAM;IACnC;AACF;AAEO,SAAS;IACd,OAAO,MAAM,IAAI,CAAC;AACpB"}}, {"offset": {"line": 87, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[turbopack-node]/transforms/postcss.ts"], "sourcesContent": ["declare const __turbopack_external_require__: (\r\n  id: string,\r\n  thunk: () => any,\r\n  esm?: boolean\r\n) => any\r\n\r\nimport type { Processor } from 'postcss'\r\n\r\n// @ts-ignore\r\nimport postcss from '@vercel/turbopack/postcss'\r\n// @ts-ignore\r\nimport importedConfig from 'CONFIG'\r\nimport { getReadEnvVariables, toPath, type TransformIpc } from './transforms'\r\n\r\nlet processor: Processor | undefined\r\n\r\nexport const init = async (ipc: TransformIpc) => {\r\n  let config = importedConfig\r\n  if (typeof config === 'function') {\r\n    config = await config({ env: 'development' })\r\n  }\r\n  if (typeof config === 'undefined') {\r\n    throw new Error(\r\n      'PostCSS config is undefined (make sure to export an function or object from config file)'\r\n    )\r\n  }\r\n  let plugins: any[]\r\n  if (Array.isArray(config.plugins)) {\r\n    plugins = config.plugins.map((plugin: [string, any] | string | any) => {\r\n      if (Array.isArray(plugin)) {\r\n        return plugin\r\n      } else if (typeof plugin === 'string') {\r\n        return [plugin, {}]\r\n      } else {\r\n        return plugin\r\n      }\r\n    })\r\n  } else if (typeof config.plugins === 'object') {\r\n    plugins = Object.entries(config.plugins).filter(([, options]) => options)\r\n  } else {\r\n    plugins = []\r\n  }\r\n  const loadedPlugins = plugins.map((plugin) => {\r\n    if (Array.isArray(plugin)) {\r\n      const [arg, options] = plugin\r\n      let pluginFactory = arg\r\n\r\n      if (typeof pluginFactory === 'string') {\r\n        pluginFactory = require(/* turbopackIgnore: true */ pluginFactory)\r\n      }\r\n\r\n      if (pluginFactory.default) {\r\n        pluginFactory = pluginFactory.default\r\n      }\r\n\r\n      return pluginFactory(options)\r\n    }\r\n    return plugin\r\n  })\r\n\r\n  processor = postcss(loadedPlugins)\r\n}\r\n\r\nexport default async function transform(\r\n  ipc: TransformIpc,\r\n  cssContent: string,\r\n  name: string,\r\n  sourceMap: boolean\r\n) {\r\n  const { css, map, messages } = await processor!.process(cssContent, {\r\n    from: name,\r\n    to: name,\r\n    map: sourceMap\r\n      ? {\r\n          inline: false,\r\n          annotation: false,\r\n        }\r\n      : undefined,\r\n  })\r\n\r\n  const assets = []\r\n  const filePaths: string[] = []\r\n  const buildFilePaths: string[] = []\r\n  const directories: Array<[string, string]> = []\r\n\r\n  for (const msg of messages) {\r\n    switch (msg.type) {\r\n      case 'asset':\r\n        assets.push({\r\n          file: msg.file,\r\n          content: msg.content,\r\n          sourceMap: !sourceMap\r\n            ? undefined\r\n            : typeof msg.sourceMap === 'string'\r\n              ? msg.sourceMap\r\n              : JSON.stringify(msg.sourceMap),\r\n          // There is also an info field, which we currently ignore\r\n        })\r\n        break\r\n      case 'dependency':\r\n      case 'missing-dependency':\r\n        filePaths.push(toPath(msg.file))\r\n        break\r\n      case 'build-dependency':\r\n        buildFilePaths.push(toPath(msg.file))\r\n        break\r\n      case 'dir-dependency':\r\n        directories.push([toPath(msg.dir), msg.glob])\r\n        break\r\n      case 'context-dependency':\r\n        directories.push([toPath(msg.dir), '**'])\r\n        break\r\n      default:\r\n        // TODO: do we need to do anything here?\r\n        break\r\n    }\r\n  }\r\n  ipc.sendInfo({\r\n    type: 'dependencies',\r\n    filePaths,\r\n    directories,\r\n    buildFilePaths,\r\n    envVariables: getReadEnvVariables(),\r\n  })\r\n  return {\r\n    css,\r\n    map: sourceMap ? JSON.stringify(map) : undefined,\r\n    assets,\r\n  }\r\n}\r\n"], "names": [], "mappings": ";;;;AAQA,aAAa;AACb;AACA,aAAa;AACb;AACA;;;;AAEA,IAAI;AAEG,MAAM,OAAO,OAAO;IACzB,IAAI,SAAS,+GAAA,CAAA,UAAc;IAC3B,IAAI,OAAO,WAAW,YAAY;QAChC,SAAS,MAAM,OAAO;YAAE,KAAK;QAAc;IAC7C;IACA,IAAI,OAAO,WAAW,aAAa;QACjC,MAAM,IAAI,MACR;IAEJ;IACA,IAAI;IACJ,IAAI,MAAM,OAAO,CAAC,OAAO,OAAO,GAAG;QACjC,UAAU,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;YAC5B,IAAI,MAAM,OAAO,CAAC,SAAS;gBACzB,OAAO;YACT,OAAO,IAAI,OAAO,WAAW,UAAU;gBACrC,OAAO;oBAAC;oBAAQ,CAAC;iBAAE;YACrB,OAAO;gBACL,OAAO;YACT;QACF;IACF,OAAO,IAAI,OAAO,OAAO,OAAO,KAAK,UAAU;QAC7C,UAAU,OAAO,OAAO,CAAC,OAAO,OAAO,EAAE,MAAM,CAAC,CAAC,GAAG,QAAQ,GAAK;IACnE,OAAO;QACL,UAAU,EAAE;IACd;IACA,MAAM,gBAAgB,QAAQ,GAAG,CAAC,CAAC;QACjC,IAAI,MAAM,OAAO,CAAC,SAAS;YACzB,MAAM,CAAC,KAAK,QAAQ,GAAG;YACvB,IAAI,gBAAgB;YAEpB,IAAI,OAAO,kBAAkB,UAAU;gBACrC,gBAAgB,QAAQ,yBAAyB,GAAG;YACtD;YAEA,IAAI,cAAc,OAAO,EAAE;gBACzB,gBAAgB,cAAc,OAAO;YACvC;YAEA,OAAO,cAAc;QACvB;QACA,OAAO;IACT;IAEA,YAAY,CAAA,GAAA,uIAAA,CAAA,UAAO,AAAD,EAAE;AACtB;AAEe,eAAe,UAC5B,GAAiB,EACjB,UAAkB,EAClB,IAAY,EACZ,SAAkB;IAElB,MAAM,EAAE,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,GAAG,MAAM,UAAW,OAAO,CAAC,YAAY;QAClE,MAAM;QACN,IAAI;QACJ,KAAK,YACD;YACE,QAAQ;YACR,YAAY;QACd,IACA;IACN;IAEA,MAAM,SAAS,EAAE;IACjB,MAAM,YAAsB,EAAE;IAC9B,MAAM,iBAA2B,EAAE;IACnC,MAAM,cAAuC,EAAE;IAE/C,KAAK,MAAM,OAAO,SAAU;QAC1B,OAAQ,IAAI,IAAI;YACd,KAAK;gBACH,OAAO,IAAI,CAAC;oBACV,MAAM,IAAI,IAAI;oBACd,SAAS,IAAI,OAAO;oBACpB,WAAW,CAAC,YACR,YACA,OAAO,IAAI,SAAS,KAAK,WACvB,IAAI,SAAS,GACb,KAAK,SAAS,CAAC,IAAI,SAAS;gBAEpC;gBACA;YACF,KAAK;YACL,KAAK;gBACH,UAAU,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,IAAI,IAAI;gBAC9B;YACF,KAAK;gBACH,eAAe,IAAI,CAAC,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,IAAI,IAAI;gBACnC;YACF,KAAK;gBACH,YAAY,IAAI,CAAC;oBAAC,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,IAAI,GAAG;oBAAG,IAAI,IAAI;iBAAC;gBAC5C;YACF,KAAK;gBACH,YAAY,IAAI,CAAC;oBAAC,CAAA,GAAA,+HAAA,CAAA,SAAM,AAAD,EAAE,IAAI,GAAG;oBAAG;iBAAK;gBACxC;YACF;gBAEE;QACJ;IACF;IACA,IAAI,QAAQ,CAAC;QACX,MAAM;QACN;QACA;QACA;QACA,cAAc,CAAA,GAAA,+HAAA,CAAA,sBAAmB,AAAD;IAClC;IACA,OAAO;QACL;QACA,KAAK,YAAY,KAAK,SAAS,CAAC,OAAO;QACvC;IACF;AACF"}}]}