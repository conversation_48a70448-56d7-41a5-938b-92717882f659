import type { SessionStep, Timer, CookingStep } from '@/types';

export interface CookingNotification {
  id: string;
  type: 'step_start' | 'step_reminder' | 'timer_complete' | 'attention_needed' | 'prep_reminder';
  title: string;
  message: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  timestamp: Date;
  actionRequired?: boolean;
  relatedStepId?: string;
  relatedTimerId?: string;
}

export class NotificationManager {
  private notifications: CookingNotification[] = [];
  private listeners: Array<(notification: CookingNotification) => void> = [];
  private scheduledNotifications = new Map<string, NodeJS.Timeout>();

  constructor() {
    this.requestNotificationPermission();
  }

  /**
   * Request browser notification permission
   */
  async requestNotificationPermission(): Promise<boolean> {
    if (!('Notification' in window)) {
      console.warn('This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') {
      return true;
    }

    if (Notification.permission === 'denied') {
      return false;
    }

    const permission = await Notification.requestPermission();
    return permission === 'granted';
  }

  /**
   * Add a notification listener
   */
  addListener(callback: (notification: CookingNotification) => void): () => void {
    this.listeners.push(callback);
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  /**
   * Send a notification
   */
  notify(notification: Omit<CookingNotification, 'id' | 'timestamp'>): void {
    const fullNotification: CookingNotification = {
      ...notification,
      id: `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
    };

    this.notifications.push(fullNotification);
    this.listeners.forEach(listener => listener(fullNotification));

    // Show browser notification for high priority items
    if (fullNotification.priority === 'high' || fullNotification.priority === 'urgent') {
      this.showBrowserNotification(fullNotification);
    }

    // Play sound for urgent notifications
    if (fullNotification.priority === 'urgent') {
      this.playNotificationSound();
    }
  }

  /**
   * Schedule notifications for cooking steps
   */
  scheduleStepNotifications(sessionSteps: SessionStep[]): void {
    // Clear existing scheduled notifications
    this.clearScheduledNotifications();

    sessionSteps.forEach(step => {
      const startTime = new Date(step.scheduled_start_time);
      const endTime = new Date(step.scheduled_end_time);
      const now = new Date();

      // Schedule start notification
      if (startTime > now) {
        const timeUntilStart = startTime.getTime() - now.getTime();
        const timeoutId = setTimeout(() => {
          this.notify({
            type: 'step_start',
            title: 'Time to start next step!',
            message: `Start: ${step.step?.title || 'Cooking step'}`,
            priority: 'high',
            actionRequired: true,
            relatedStepId: step.id,
          });
        }, timeUntilStart);

        this.scheduledNotifications.set(`step-start-${step.id}`, timeoutId);
      }

      // Schedule reminder 2 minutes before step ends (for attention-requiring steps)
      const reminderTime = new Date(endTime.getTime() - 2 * 60 * 1000); // 2 minutes before
      if (reminderTime > now && this.requiresAttention(step.step)) {
        const timeUntilReminder = reminderTime.getTime() - now.getTime();
        const timeoutId = setTimeout(() => {
          this.notify({
            type: 'step_reminder',
            title: 'Step ending soon',
            message: `${step.step?.title || 'Cooking step'} will finish in 2 minutes`,
            priority: 'medium',
            relatedStepId: step.id,
          });
        }, timeUntilReminder);

        this.scheduledNotifications.set(`step-reminder-${step.id}`, timeoutId);
      }
    });
  }

  /**
   * Schedule timer completion notifications
   */
  scheduleTimerNotifications(timers: Timer[]): void {
    timers.forEach(timer => {
      if (timer.status === 'running' && timer.end_time) {
        const endTime = new Date(timer.end_time);
        const now = new Date();

        if (endTime > now) {
          const timeUntilEnd = endTime.getTime() - now.getTime();
          const timeoutId = setTimeout(() => {
            this.notify({
              type: 'timer_complete',
              title: 'Timer Complete!',
              message: `${timer.name} has finished`,
              priority: 'urgent',
              actionRequired: true,
              relatedTimerId: timer.id,
            });
          }, timeUntilEnd);

          this.scheduledNotifications.set(`timer-${timer.id}`, timeoutId);
        }
      }
    });
  }

  /**
   * Send prep reminders
   */
  sendPrepReminders(prepSteps: SessionStep[], minutesAhead: number = 15): void {
    const now = new Date();
    const reminderTime = new Date(now.getTime() + minutesAhead * 60 * 1000);

    prepSteps.forEach(step => {
      const stepStart = new Date(step.scheduled_start_time);
      
      if (stepStart <= reminderTime && stepStart > now) {
        this.notify({
          type: 'prep_reminder',
          title: 'Prep reminder',
          message: `Start preparing: ${step.step?.title || 'Prep step'} in ${minutesAhead} minutes`,
          priority: 'medium',
          relatedStepId: step.id,
        });
      }
    });
  }

  /**
   * Clear all scheduled notifications
   */
  clearScheduledNotifications(): void {
    this.scheduledNotifications.forEach(timeoutId => {
      clearTimeout(timeoutId);
    });
    this.scheduledNotifications.clear();
  }

  /**
   * Get all notifications
   */
  getNotifications(): CookingNotification[] {
    return [...this.notifications];
  }

  /**
   * Get unread notifications
   */
  getUnreadNotifications(): CookingNotification[] {
    // For now, return all notifications
    // In a real app, you'd track read status
    return this.notifications.filter(n => 
      Date.now() - n.timestamp.getTime() < 5 * 60 * 1000 // Last 5 minutes
    );
  }

  /**
   * Clear old notifications
   */
  clearOldNotifications(olderThanMinutes: number = 60): void {
    const cutoffTime = new Date(Date.now() - olderThanMinutes * 60 * 1000);
    this.notifications = this.notifications.filter(n => n.timestamp > cutoffTime);
  }

  /**
   * Show browser notification
   */
  private showBrowserNotification(notification: CookingNotification): void {
    if (Notification.permission === 'granted') {
      const browserNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/favicon.ico',
        tag: notification.id,
        requireInteraction: notification.actionRequired,
      });

      // Auto-close after 10 seconds unless action is required
      if (!notification.actionRequired) {
        setTimeout(() => {
          browserNotification.close();
        }, 10000);
      }
    }
  }

  /**
   * Play notification sound
   */
  private playNotificationSound(): void {
    try {
      // Try to play a notification sound
      const audio = new Audio('/sounds/notification.mp3');
      audio.play().catch(() => {
        // Fallback to system beep
        console.log('🔔 Notification sound');
      });
    } catch (error) {
      console.log('🔔 Notification');
    }
  }

  /**
   * Check if a step requires attention
   */
  private requiresAttention(step?: CookingStep): boolean {
    if (!step) return false;
    
    const description = step.description.toLowerCase();
    return description.includes('stir') ||
           description.includes('flip') ||
           description.includes('check') ||
           description.includes('watch') ||
           description.includes('monitor') ||
           description.includes('adjust');
  }
}

// Global notification manager instance
export const notificationManager = new NotificationManager();

// Hook for using notifications in React components
export function useNotifications() {
  const [notifications, setNotifications] = React.useState<CookingNotification[]>([]);

  React.useEffect(() => {
    const unsubscribe = notificationManager.addListener((notification) => {
      setNotifications(prev => [notification, ...prev].slice(0, 50)); // Keep last 50
    });

    // Load existing notifications
    setNotifications(notificationManager.getNotifications());

    return unsubscribe;
  }, []);

  return {
    notifications,
    unreadCount: notifications.filter(n => 
      Date.now() - n.timestamp.getTime() < 5 * 60 * 1000
    ).length,
    notify: (notification: Omit<CookingNotification, 'id' | 'timestamp'>) => 
      notificationManager.notify(notification),
    clearOld: () => notificationManager.clearOldNotifications(),
  };
}

// Note: React import would be added at the top in a real implementation
declare const React: any;
