'use client';

import { useState, useEffect } from 'react';
import { 
  CheckCir<PERSON>, 
  Clock, 
  ChefHat, 
  Users, 
  Knife,
  Play,
  ArrowRight
} from 'lucide-react';
import type { SessionStep, CookingSession, Recipe } from '@/types';

interface PrepModeProps {
  session: CookingSession;
  prepSteps: SessionStep[];
  onCompleteStep: (stepId: string) => void;
  onStartCookMode: () => void;
  className?: string;
}

interface ConsolidatedPrepStep {
  id: string;
  title: string;
  description: string;
  recipes: string[];
  ingredients: string[];
  estimatedTime: number;
  steps: SessionStep[];
  isCompleted: boolean;
}

export default function PrepMode({
  session,
  prepSteps,
  onCompleteStep,
  onStartCookMode,
  className = ''
}: PrepModeProps) {
  const [consolidatedSteps, setConsolidatedSteps] = useState<ConsolidatedPrepStep[]>([]);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);

  // Consolidate prep steps by type
  useEffect(() => {
    const consolidated = consolidatePrepSteps(prepSteps);
    setConsolidatedSteps(consolidated);
  }, [prepSteps]);

  const completedSteps = consolidatedSteps.filter(step => step.isCompleted).length;
  const totalSteps = consolidatedSteps.length;
  const progressPercentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;

  const handleCompleteConsolidatedStep = (consolidatedStepId: string) => {
    // Mark all individual steps in this consolidated step as complete
    const consolidatedStep = consolidatedSteps.find(s => s.id === consolidatedStepId);
    if (consolidatedStep) {
      consolidatedStep.steps.forEach(step => {
        onCompleteStep(step.id);
      });

      // Update local state
      setConsolidatedSteps(prev => 
        prev.map(step => 
          step.id === consolidatedStepId 
            ? { ...step, isCompleted: true }
            : step
        )
      );

      // Move to next step
      if (currentStepIndex < totalSteps - 1) {
        setCurrentStepIndex(currentStepIndex + 1);
      }
    }
  };

  const allPrepCompleted = completedSteps === totalSteps && totalSteps > 0;

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <h1 className="text-2xl font-bold text-gray-900 flex items-center">
            <Knife className="mr-3 h-6 w-6 text-purple-600" />
            Prep Mode
          </h1>
          <div className="text-sm text-gray-600">
            Session: {session.name}
          </div>
        </div>

        {/* Progress Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
          <div className="bg-purple-50 rounded-lg p-4">
            <div className="flex items-center">
              <Knife className="h-5 w-5 text-purple-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-purple-900">Prep Steps</p>
                <p className="text-lg font-bold text-purple-600">{totalSteps}</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 rounded-lg p-4">
            <div className="flex items-center">
              <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-green-900">Completed</p>
                <p className="text-lg font-bold text-green-600">{completedSteps}</p>
              </div>
            </div>
          </div>

          <div className="bg-blue-50 rounded-lg p-4">
            <div className="flex items-center">
              <Clock className="h-5 w-5 text-blue-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-blue-900">Est. Time</p>
                <p className="text-lg font-bold text-blue-600">
                  {consolidatedSteps.reduce((total, step) => total + step.estimatedTime, 0)}m
                </p>
              </div>
            </div>
          </div>

          <div className="bg-orange-50 rounded-lg p-4">
            <div className="flex items-center">
              <Users className="h-5 w-5 text-orange-600 mr-2" />
              <div>
                <p className="text-sm font-medium text-orange-900">Recipes</p>
                <p className="text-lg font-bold text-orange-600">
                  {session.recipes?.length || 0}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-4">
          <div className="flex justify-between text-sm text-gray-600 mb-1">
            <span>Prep Progress</span>
            <span>{progressPercentage}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className="bg-purple-600 h-3 rounded-full transition-all duration-300"
              style={{ width: `${progressPercentage}%` }}
            />
          </div>
        </div>

        {/* Completion Status */}
        {allPrepCompleted && (
          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                <div>
                  <h3 className="text-sm font-medium text-green-800">
                    All prep work completed!
                  </h3>
                  <p className="text-sm text-green-700">
                    Ready to start cooking. All ingredients are prepared and ready to go.
                  </p>
                </div>
              </div>
              <button
                onClick={onStartCookMode}
                className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Start Cooking
                <ArrowRight className="ml-2 h-4 w-4" />
              </button>
            </div>
          </div>
        )}
      </div>

      {/* Current Step */}
      {!allPrepCompleted && currentStepIndex < totalSteps && (
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
            <Play className="mr-2 h-5 w-5 text-blue-600" />
            Current Step ({currentStepIndex + 1} of {totalSteps})
          </h2>
          <CurrentPrepStepCard
            step={consolidatedSteps[currentStepIndex]}
            onComplete={() => handleCompleteConsolidatedStep(consolidatedSteps[currentStepIndex].id)}
          />
        </div>
      )}

      {/* All Prep Steps */}
      <div className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">All Prep Steps</h2>
        <div className="space-y-4">
          {consolidatedSteps.map((step, index) => (
            <PrepStepCard
              key={step.id}
              step={step}
              stepNumber={index + 1}
              isActive={index === currentStepIndex && !allPrepCompleted}
              onComplete={() => handleCompleteConsolidatedStep(step.id)}
            />
          ))}
        </div>
      </div>
    </div>
  );
}

interface CurrentPrepStepCardProps {
  step: ConsolidatedPrepStep;
  onComplete: () => void;
}

function CurrentPrepStepCard({ step, onComplete }: CurrentPrepStepCardProps) {
  return (
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
      <div className="flex items-start justify-between">
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">
            {step.title}
          </h3>
          <p className="text-blue-800 mb-4">
            {step.description}
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <h4 className="text-sm font-medium text-blue-900 mb-2">Recipes:</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                {step.recipes.map(recipe => (
                  <li key={recipe}>• {recipe}</li>
                ))}
              </ul>
            </div>
            
            {step.ingredients.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-blue-900 mb-2">Ingredients:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  {step.ingredients.map(ingredient => (
                    <li key={ingredient}>• {ingredient}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>

          <div className="flex items-center text-sm text-blue-700">
            <Clock className="h-4 w-4 mr-1" />
            Estimated time: {step.estimatedTime} minutes
          </div>
        </div>

        <button
          onClick={onComplete}
          className="ml-4 px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 font-medium"
        >
          Complete Step
        </button>
      </div>
    </div>
  );
}

interface PrepStepCardProps {
  step: ConsolidatedPrepStep;
  stepNumber: number;
  isActive: boolean;
  onComplete: () => void;
}

function PrepStepCard({ step, stepNumber, isActive, onComplete }: PrepStepCardProps) {
  const getCardStyle = () => {
    if (step.isCompleted) return 'bg-green-50 border-green-200';
    if (isActive) return 'bg-blue-50 border-blue-200';
    return 'bg-gray-50 border-gray-200';
  };

  const getTextStyle = () => {
    if (step.isCompleted) return 'text-green-800';
    if (isActive) return 'text-blue-800';
    return 'text-gray-700';
  };

  return (
    <div className={`border rounded-lg p-4 ${getCardStyle()}`}>
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3">
          <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
            step.isCompleted 
              ? 'bg-green-600 text-white' 
              : isActive 
                ? 'bg-blue-600 text-white'
                : 'bg-gray-400 text-white'
          }`}>
            {step.isCompleted ? (
              <CheckCircle className="h-4 w-4" />
            ) : (
              <span className="text-sm font-medium">{stepNumber}</span>
            )}
          </div>

          <div className="flex-1">
            <h3 className={`font-medium ${getTextStyle()}`}>
              {step.title}
            </h3>
            <p className={`text-sm ${getTextStyle()} mt-1`}>
              {step.description}
            </p>
            
            <div className="flex items-center space-x-4 mt-2 text-xs">
              <span className={getTextStyle()}>
                <Clock className="inline h-3 w-3 mr-1" />
                {step.estimatedTime}m
              </span>
              <span className={getTextStyle()}>
                <Users className="inline h-3 w-3 mr-1" />
                {step.recipes.length} recipe{step.recipes.length !== 1 ? 's' : ''}
              </span>
            </div>
          </div>
        </div>

        {!step.isCompleted && (
          <button
            onClick={onComplete}
            disabled={!isActive}
            className={`px-3 py-2 rounded-md text-sm font-medium ${
              isActive
                ? 'bg-blue-600 text-white hover:bg-blue-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Complete
          </button>
        )}
      </div>
    </div>
  );
}

// Helper function to consolidate prep steps
function consolidatePrepSteps(prepSteps: SessionStep[]): ConsolidatedPrepStep[] {
  const groups = new Map<string, SessionStep[]>();

  // Group steps by similar activities
  prepSteps.forEach(step => {
    const key = getStepGroupKey(step.step?.description || '');
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key)!.push(step);
  });

  // Convert groups to consolidated steps
  return Array.from(groups.entries()).map(([key, steps], index) => {
    const recipes = Array.from(new Set(steps.map(s => s.recipe?.name || 'Unknown Recipe')));
    const ingredients = Array.from(new Set(
      steps.flatMap(s => s.step?.description.match(/\b\w+(?:\s+\w+)*\b/g) || [])
    )).slice(0, 5); // Limit to 5 key ingredients

    const estimatedTime = Math.max(...steps.map(s => s.step?.duration_minutes || 5));
    const allCompleted = steps.every(s => s.status === 'completed');

    return {
      id: `consolidated-${index}`,
      title: key,
      description: generateConsolidatedDescription(key, recipes),
      recipes,
      ingredients,
      estimatedTime,
      steps,
      isCompleted: allCompleted,
    };
  });
}

function getStepGroupKey(description: string): string {
  const desc = description.toLowerCase();
  
  if (desc.includes('chop') || desc.includes('dice') || desc.includes('cut')) {
    return 'Chopping & Cutting';
  }
  if (desc.includes('mince') || desc.includes('garlic')) {
    return 'Mincing';
  }
  if (desc.includes('measure') || desc.includes('prep')) {
    return 'Measuring & Prep';
  }
  if (desc.includes('wash') || desc.includes('clean')) {
    return 'Washing & Cleaning';
  }
  if (desc.includes('mix') || desc.includes('combine')) {
    return 'Mixing & Combining';
  }
  
  return 'General Prep';
}

function generateConsolidatedDescription(groupKey: string, recipes: string[]): string {
  const recipeList = recipes.length > 2 
    ? `${recipes.slice(0, 2).join(', ')} and ${recipes.length - 2} other recipe${recipes.length - 2 !== 1 ? 's' : ''}`
    : recipes.join(' and ');

  switch (groupKey) {
    case 'Chopping & Cutting':
      return `Chop, dice, and cut all ingredients needed for ${recipeList}`;
    case 'Mincing':
      return `Mince garlic and other aromatics for ${recipeList}`;
    case 'Measuring & Prep':
      return `Measure and prepare all ingredients for ${recipeList}`;
    case 'Washing & Cleaning':
      return `Wash and clean all produce for ${recipeList}`;
    case 'Mixing & Combining':
      return `Mix and combine ingredients as needed for ${recipeList}`;
    default:
      return `Complete preparation tasks for ${recipeList}`;
  }
}
