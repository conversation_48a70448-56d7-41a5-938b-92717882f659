import { supabase, TABLES, handleSupabaseError } from './supabase';
import type { 
  Recipe, 
  CookingSession, 
  Ingredient, 
  RecipeIngredient, 
  CookingStep,
  SessionStep,
  Timer,
  ShoppingListItem,
  ApiResponse 
} from '@/types';

// Recipe operations
export async function getRecipes(userId: string): Promise<ApiResponse<Recipe[]>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.RECIPES)
      .select(`
        *,
        ingredients:recipe_ingredients(
          *,
          ingredient:ingredients(*)
        ),
        steps:cooking_steps(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) return handleSupabaseError(error);
    return { data: data || [] };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function getRecipe(id: string): Promise<ApiResponse<Recipe>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.RECIPES)
      .select(`
        *,
        ingredients:recipe_ingredients(
          *,
          ingredient:ingredients(*)
        ),
        steps:cooking_steps(*)
      `)
      .eq('id', id)
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function createRecipe(recipe: Omit<Recipe, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<Recipe>> {
  try {
    // Start a transaction
    const { data: recipeData, error: recipeError } = await supabase
      .from(TABLES.RECIPES)
      .insert({
        user_id: recipe.user_id,
        name: recipe.name,
        description: recipe.description,
        servings: recipe.servings,
        total_prep_time: recipe.total_prep_time,
        total_cook_time: recipe.total_cook_time,
        difficulty_level: recipe.difficulty_level,
        cuisine_type: recipe.cuisine_type,
        dietary_tags: recipe.dietary_tags,
      })
      .select()
      .single();

    if (recipeError) return handleSupabaseError(recipeError);

    const recipeId = recipeData.id;

    // Insert ingredients
    if (recipe.ingredients.length > 0) {
      const ingredientsToInsert = recipe.ingredients.map(ing => ({
        recipe_id: recipeId,
        ingredient_id: ing.ingredient_id,
        quantity: ing.quantity,
        unit: ing.unit,
        notes: ing.notes,
      }));

      const { error: ingredientsError } = await supabase
        .from(TABLES.RECIPE_INGREDIENTS)
        .insert(ingredientsToInsert);

      if (ingredientsError) return handleSupabaseError(ingredientsError);
    }

    // Insert steps
    if (recipe.steps.length > 0) {
      const stepsToInsert = recipe.steps.map(step => ({
        recipe_id: recipeId,
        step_number: step.step_number,
        title: step.title,
        description: step.description,
        duration_minutes: step.duration_minutes,
        temperature: step.temperature,
        temperature_unit: step.temperature_unit,
        equipment: step.equipment,
        is_prep_step: step.is_prep_step,
        can_be_done_ahead: step.can_be_done_ahead,
        depends_on_steps: step.depends_on_steps,
      }));

      const { error: stepsError } = await supabase
        .from(TABLES.COOKING_STEPS)
        .insert(stepsToInsert);

      if (stepsError) return handleSupabaseError(stepsError);
    }

    // Fetch the complete recipe with relations
    return await getRecipe(recipeId);
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function updateRecipe(id: string, updates: Partial<Recipe>): Promise<ApiResponse<Recipe>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.RECIPES)
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function deleteRecipe(id: string): Promise<ApiResponse<boolean>> {
  try {
    const { error } = await supabase
      .from(TABLES.RECIPES)
      .delete()
      .eq('id', id);

    if (error) return handleSupabaseError(error);
    return { data: true };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

// Ingredient operations
export async function getIngredients(): Promise<ApiResponse<Ingredient[]>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .select('*')
      .order('name');

    if (error) return handleSupabaseError(error);
    return { data: data || [] };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function searchIngredients(query: string): Promise<ApiResponse<Ingredient[]>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.INGREDIENTS)
      .select('*')
      .ilike('name', `%${query}%`)
      .order('name')
      .limit(20);

    if (error) return handleSupabaseError(error);
    return { data: data || [] };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

// Cooking session operations
export async function getCookingSessions(userId: string): Promise<ApiResponse<CookingSession[]>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.COOKING_SESSIONS)
      .select(`
        *,
        recipes:recipes(*)
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false });

    if (error) return handleSupabaseError(error);
    return { data: data || [] };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function getCookingSession(id: string): Promise<ApiResponse<CookingSession>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.COOKING_SESSIONS)
      .select(`
        *,
        recipes:recipes(
          *,
          ingredients:recipe_ingredients(
            *,
            ingredient:ingredients(*)
          ),
          steps:cooking_steps(*)
        )
      `)
      .eq('id', id)
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function createCookingSession(session: Omit<CookingSession, 'id' | 'created_at' | 'updated_at'>): Promise<ApiResponse<CookingSession>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.COOKING_SESSIONS)
      .insert(session)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function updateCookingSession(id: string, updates: Partial<CookingSession>): Promise<ApiResponse<CookingSession>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.COOKING_SESSIONS)
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

// Timer operations
export async function getTimers(sessionId: string): Promise<ApiResponse<Timer[]>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.TIMERS)
      .select('*')
      .eq('session_id', sessionId)
      .order('created_at');

    if (error) return handleSupabaseError(error);
    return { data: data || [] };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function createTimer(timer: Omit<Timer, 'id' | 'created_at'>): Promise<ApiResponse<Timer>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.TIMERS)
      .insert(timer)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}

export async function updateTimer(id: string, updates: Partial<Timer>): Promise<ApiResponse<Timer>> {
  try {
    const { data, error } = await supabase
      .from(TABLES.TIMERS)
      .update(updates)
      .eq('id', id)
      .select()
      .single();

    if (error) return handleSupabaseError(error);
    return { data };
  } catch (error) {
    return handleSupabaseError(error);
  }
}
