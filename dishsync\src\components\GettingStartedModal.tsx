'use client';

import { useState } from 'react';
import { X, ChefHat, Clock, Users, Zap, ArrowRight, ArrowLeft } from 'lucide-react';
import Image from 'next/image';

interface GettingStartedModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function GettingStartedModal({ isOpen, onClose }: GettingStartedModalProps) {
  const [currentStep, setCurrentStep] = useState(0);

  const steps = [
    {
      title: "WELCOME TO DISHSYNC!",
      subtitle: "Your Ultimate Cooking Companion",
      content: "DishSync is the REVOLUTIONARY multi-dish cooking assistant that will transform your kitchen experience! Say goodbye to burnt food and timing disasters!",
      image: "/assets/chef.png",
      bgColor: "bg-yellow-400",
      textColor: "text-black"
    },
    {
      title: "ADD YOUR RECIPES",
      subtitle: "Build Your Recipe Collection",
      content: "Start by adding your favorite recipes! Include ingredients, cooking steps, prep time, and cooking instructions. The more detailed, the better DishSync can help you!",
      icon: <ChefHat className="h-16 w-16" />,
      bgColor: "bg-blue-400",
      textColor: "text-black"
    },
    {
      title: "CREATE COOKING SESSIONS",
      subtitle: "Coordinate Multiple Dishes",
      content: "Select multiple recipes for your meal and let DishSync create the PERFECT timeline! It will tell you exactly when to start each step for flawless coordination!",
      icon: <Clock className="h-16 w-16" />,
      bgColor: "bg-green-400",
      textColor: "text-black"
    },
    {
      title: "PREP MODE MAGIC",
      subtitle: "Consolidate Your Preparation",
      content: "DishSync automatically groups similar prep tasks together! Chop all your onions at once, measure all spices together - MAXIMUM efficiency!",
      icon: <Users className="h-16 w-16" />,
      bgColor: "bg-purple-400",
      textColor: "text-black"
    },
    {
      title: "COOK LIKE A PRO!",
      subtitle: "Follow the Perfect Timeline",
      content: "Enter Cook Mode and follow the step-by-step timeline! Real-time timers, notifications, and guidance ensure PERFECT results every time!",
      icon: <Zap className="h-16 w-16" />,
      bgColor: "bg-red-400",
      textColor: "text-white"
    }
  ];

  if (!isOpen) return null;

  const currentStepData = steps[currentStep];
  const isLastStep = currentStep === steps.length - 1;
  const isFirstStep = currentStep === 0;

  const nextStep = () => {
    if (!isLastStep) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (!isFirstStep) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    setCurrentStep(0);
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className={`${currentStepData.bgColor} border-8 border-black shadow-[16px_16px_0px_0px_rgba(0,0,0,1)] max-w-4xl w-full max-h-[90vh] overflow-y-auto transform rotate-1`}>
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b-4 border-black">
          <div className="flex items-center space-x-4">
            <div className="bg-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)]">
              <Image 
                src="/assets/logo.png" 
                alt="DishSync Logo" 
                width={32} 
                height={32}
              />
            </div>
            <h2 className={`text-2xl font-black ${currentStepData.textColor}`}>
              GETTING STARTED GUIDE
            </h2>
          </div>
          <button
            onClick={handleClose}
            className="bg-red-500 text-white border-4 border-black p-2 shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Bar */}
        <div className="p-6 border-b-4 border-black">
          <div className="flex justify-between items-center mb-4">
            <span className={`font-black ${currentStepData.textColor}`}>
              STEP {currentStep + 1} OF {steps.length}
            </span>
            <span className={`font-black ${currentStepData.textColor}`}>
              {Math.round(((currentStep + 1) / steps.length) * 100)}%
            </span>
          </div>
          <div className="w-full bg-black border-2 border-black h-4">
            <div
              className="bg-white h-full transition-all duration-300 border-r-2 border-black"
              style={{ width: `${((currentStep + 1) / steps.length) * 100}%` }}
            />
          </div>
        </div>

        {/* Content */}
        <div className="p-8">
          <div className="text-center mb-8">
            <h1 className={`text-5xl font-black ${currentStepData.textColor} mb-4 transform -rotate-2`}>
              {currentStepData.title}
            </h1>
            <h2 className={`text-2xl font-bold ${currentStepData.textColor} transform rotate-1`}>
              {currentStepData.subtitle}
            </h2>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            {/* Image/Icon */}
            <div className="flex justify-center">
              {currentStepData.image ? (
                <div className="bg-white border-4 border-black p-6 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform -rotate-3">
                  <Image
                    src={currentStepData.image}
                    alt="Chef illustration"
                    width={300}
                    height={300}
                    className="rounded-lg"
                  />
                </div>
              ) : (
                <div className="bg-white border-4 border-black p-8 shadow-[8px_8px_0px_0px_rgba(0,0,0,1)] transform rotate-3">
                  {currentStepData.icon}
                </div>
              )}
            </div>

            {/* Content */}
            <div className="space-y-6">
              <div className="bg-white border-4 border-black p-6 shadow-[6px_6px_0px_0px_rgba(0,0,0,1)] transform rotate-1">
                <p className="text-xl font-bold text-black leading-relaxed">
                  {currentStepData.content}
                </p>
              </div>

              {/* Step-specific tips */}
              {currentStep === 0 && (
                <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                  <p className="text-white font-bold">
                    💡 TIP: DishSync works best when you have multiple recipes to coordinate!
                  </p>
                </div>
              )}

              {currentStep === 1 && (
                <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                  <p className="text-white font-bold">
                    💡 TIP: Include prep times and cooking temperatures for best results!
                  </p>
                </div>
              )}

              {currentStep === 2 && (
                <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                  <p className="text-white font-bold">
                    💡 TIP: DishSync automatically calculates the optimal start times!
                  </p>
                </div>
              )}

              {currentStep === 3 && (
                <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                  <p className="text-white font-bold">
                    💡 TIP: Prep Mode saves you time by grouping similar tasks!
                  </p>
                </div>
              )}

              {currentStep === 4 && (
                <div className="bg-black border-4 border-white p-4 shadow-[4px_4px_0px_0px_rgba(255,255,255,1)]">
                  <p className="text-white font-bold">
                    💡 TIP: Use the built-in timers to never overcook anything again!
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex justify-between items-center p-6 border-t-4 border-black">
          <button
            onClick={prevStep}
            disabled={isFirstStep}
            className={`flex items-center px-6 py-3 font-black text-lg border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] transition-all ${
              isFirstStep
                ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                : 'bg-white text-black hover:translate-x-1 hover:translate-y-1 hover:shadow-none'
            }`}
          >
            <ArrowLeft className="mr-2 h-5 w-5" />
            BACK
          </button>

          <div className="flex space-x-2">
            {steps.map((_, index) => (
              <button
                key={index}
                onClick={() => setCurrentStep(index)}
                className={`w-4 h-4 border-2 border-black transition-all ${
                  index === currentStep
                    ? 'bg-black'
                    : 'bg-white hover:bg-gray-200'
                }`}
              />
            ))}
          </div>

          {isLastStep ? (
            <button
              onClick={handleClose}
              className="flex items-center px-6 py-3 bg-green-500 text-white font-black text-lg border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
            >
              START COOKING!
              <Zap className="ml-2 h-5 w-5" />
            </button>
          ) : (
            <button
              onClick={nextStep}
              className="flex items-center px-6 py-3 bg-blue-500 text-white font-black text-lg border-4 border-black shadow-[4px_4px_0px_0px_rgba(0,0,0,1)] hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all"
            >
              NEXT
              <ArrowRight className="ml-2 h-5 w-5" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
