import { useState, useEffect, useCallback } from 'react';
import { 
  getCookingSession, 
  updateCookingSession, 
  getTimers, 
  createTimer, 
  updateTimer 
} from '@/lib/database';
import { coordinateMultipleDishes, scaleRecipe } from '@/utils/cooking-coordinator';
import type { 
  CookingSession, 
  Timer, 
  SessionStep, 
  DashboardState,
  Recipe,
  CookingTimeline 
} from '@/types';

export function useCookingSession(sessionId: string | null) {
  const [session, setSession] = useState<CookingSession | null>(null);
  const [timeline, setTimeline] = useState<CookingTimeline | null>(null);
  const [timers, setTimers] = useState<Timer[]>([]);
  const [currentStep, setCurrentStep] = useState<SessionStep | null>(null);
  const [dashboardState, setDashboardState] = useState<DashboardState>({
    active_timers: [],
    next_steps: [],
    mode: 'planning',
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Load session data
  const loadSession = useCallback(async () => {
    if (!sessionId) return;
    
    setLoading(true);
    setError(null);

    try {
      const sessionResult = await getCookingSession(sessionId);
      if (sessionResult.error) {
        setError(sessionResult.error);
        return;
      }

      const sessionData = sessionResult.data!;
      setSession(sessionData);

      // Generate timeline if recipes are available
      if (sessionData.recipes && sessionData.recipes.length > 0) {
        const scaledRecipes = sessionData.recipes.map(recipe => 
          scaleRecipe(recipe, sessionData.target_servings[recipe.id] || recipe.servings)
        );

        const targetTime = sessionData.estimated_completion_time 
          ? new Date(sessionData.estimated_completion_time)
          : new Date(Date.now() + 2 * 60 * 60 * 1000); // Default to 2 hours from now

        const coordination = coordinateMultipleDishes(sessionData, scaledRecipes, targetTime);
        setTimeline(coordination.timeline);
      }

      // Load timers
      const timersResult = await getTimers(sessionId);
      if (timersResult.data) {
        setTimers(timersResult.data);
      }

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load session');
    } finally {
      setLoading(false);
    }
  }, [sessionId]);

  // Update dashboard state based on current data
  useEffect(() => {
    if (!session || !timeline) return;

    const activeTimers = timers.filter(timer => 
      timer.status === 'running' || timer.status === 'paused'
    );

    const now = new Date();
    const upcomingSteps = timeline.steps
      .filter(step => {
        const startTime = new Date(step.scheduled_start_time);
        const endTime = new Date(step.scheduled_end_time);
        return step.status === 'pending' && startTime <= addMinutes(now, 15); // Next 15 minutes
      })
      .slice(0, 3); // Show next 3 steps

    const currentActiveStep = timeline.steps.find(step => {
      const startTime = new Date(step.scheduled_start_time);
      const endTime = new Date(step.scheduled_end_time);
      return step.status === 'in_progress' || (now >= startTime && now <= endTime);
    });

    setCurrentStep(currentActiveStep || null);
    
    setDashboardState({
      current_session: session,
      active_timers: activeTimers,
      current_step: currentActiveStep,
      next_steps: upcomingSteps,
      mode: session.status === 'prep' ? 'prep' : session.status === 'cooking' ? 'cook' : 'planning',
    });
  }, [session, timeline, timers]);

  // Start a timer
  const startTimer = useCallback(async (
    sessionStepId: string, 
    name: string, 
    durationMinutes: number
  ) => {
    if (!sessionId) return;

    const newTimer = {
      session_id: sessionId,
      session_step_id: sessionStepId,
      name,
      duration_minutes: durationMinutes,
      start_time: new Date().toISOString(),
      end_time: addMinutes(new Date(), durationMinutes).toISOString(),
      status: 'running' as const,
      sound_enabled: true,
    };

    const result = await createTimer(newTimer);
    if (result.data) {
      setTimers(prev => [...prev, result.data!]);
    }
  }, [sessionId]);

  // Update timer status
  const updateTimerStatus = useCallback(async (timerId: string, status: Timer['status']) => {
    const result = await updateTimer(timerId, { status });
    if (result.data) {
      setTimers(prev => prev.map(timer => 
        timer.id === timerId ? result.data! : timer
      ));
    }
  }, []);

  // Start cooking session
  const startCooking = useCallback(async () => {
    if (!session) return;

    const result = await updateCookingSession(session.id, {
      status: 'cooking',
      start_time: new Date().toISOString(),
    });

    if (result.data) {
      setSession(result.data);
    }
  }, [session]);

  // Complete current step
  const completeCurrentStep = useCallback(async () => {
    if (!currentStep || !timeline) return;

    // Update step status in timeline
    const updatedSteps = timeline.steps.map(step =>
      step.id === currentStep.id
        ? { ...step, status: 'completed' as const, actual_end_time: new Date().toISOString() }
        : step
    );

    setTimeline(prev => prev ? { ...prev, steps: updatedSteps } : null);

    // Find next step
    const nextStep = timeline.steps.find(step => 
      step.status === 'pending' && 
      new Date(step.scheduled_start_time) <= addMinutes(new Date(), 5)
    );

    if (nextStep) {
      setCurrentStep(nextStep);
    }
  }, [currentStep, timeline]);

  // Load session on mount or when sessionId changes
  useEffect(() => {
    loadSession();
  }, [loadSession]);

  return {
    session,
    timeline,
    timers,
    currentStep,
    dashboardState,
    loading,
    error,
    startTimer,
    updateTimerStatus,
    startCooking,
    completeCurrentStep,
    refreshSession: loadSession,
  };
}

// Helper function to add minutes to a date
function addMinutes(date: Date, minutes: number): Date {
  return new Date(date.getTime() + minutes * 60000);
}
