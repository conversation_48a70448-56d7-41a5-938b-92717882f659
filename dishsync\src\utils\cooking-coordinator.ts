import { addMinutes, subMinutes, format } from 'date-fns';
import type { 
  Recipe, 
  CookingSession, 
  CookingStep, 
  SessionStep, 
  CookingTimeline,
  RecipeIngredient,
  ShoppingListItem 
} from '@/types';

export interface ScaledRecipe extends Recipe {
  scalingFactor: number;
  scaledIngredients: RecipeIngredient[];
  scaledSteps: CookingStep[];
}

export interface CoordinationResult {
  timeline: CookingTimeline;
  sessionSteps: Omit<SessionStep, 'id' | 'created_at'>[];
  shoppingList: Omit<ShoppingListItem, 'id' | 'created_at'>[];
  estimatedTotalTime: number;
}

/**
 * Scale a recipe based on target servings
 */
export function scaleRecipe(recipe: Recipe, targetServings: number): ScaledRecipe {
  const scalingFactor = targetServings / recipe.servings;
  
  const scaledIngredients = recipe.ingredients.map(ingredient => ({
    ...ingredient,
    quantity: ingredient.quantity * scalingFactor,
  }));

  const scaledSteps = recipe.steps.map(step => ({
    ...step,
    // Note: cooking times generally don't scale linearly, but we'll keep them the same for simplicity
    // In a real application, you might want more sophisticated scaling logic
  }));

  return {
    ...recipe,
    scalingFactor,
    scaledIngredients,
    scaledSteps,
  };
}

/**
 * Aggregate ingredients across multiple scaled recipes
 */
export function aggregateIngredients(
  scaledRecipes: ScaledRecipe[],
  sessionId: string
): Omit<ShoppingListItem, 'id' | 'created_at'>[] {
  const ingredientMap = new Map<string, {
    ingredient_id: string;
    total_quantity: number;
    unit: string;
    notes: string[];
  }>();

  scaledRecipes.forEach(recipe => {
    recipe.scaledIngredients.forEach(recipeIngredient => {
      const key = `${recipeIngredient.ingredient_id}-${recipeIngredient.unit}`;
      
      if (ingredientMap.has(key)) {
        const existing = ingredientMap.get(key)!;
        existing.total_quantity += recipeIngredient.quantity;
        if (recipeIngredient.notes) {
          existing.notes.push(recipeIngredient.notes);
        }
      } else {
        ingredientMap.set(key, {
          ingredient_id: recipeIngredient.ingredient_id,
          total_quantity: recipeIngredient.quantity,
          unit: recipeIngredient.unit,
          notes: recipeIngredient.notes ? [recipeIngredient.notes] : [],
        });
      }
    });
  });

  return Array.from(ingredientMap.values()).map(item => ({
    session_id: sessionId,
    ingredient_id: item.ingredient_id,
    total_quantity: item.total_quantity,
    unit: item.unit,
    is_purchased: false,
    notes: item.notes.length > 0 ? item.notes.join(', ') : undefined,
  }));
}

/**
 * Calculate the optimal cooking timeline for multiple dishes
 */
export function coordinateMultipleDishes(
  session: CookingSession,
  scaledRecipes: ScaledRecipe[],
  targetCompletionTime: Date
): CoordinationResult {
  // Separate prep and cooking steps
  const allSteps: Array<{
    recipe: ScaledRecipe;
    step: CookingStep;
    isPrep: boolean;
    duration: number;
  }> = [];

  scaledRecipes.forEach(recipe => {
    recipe.scaledSteps.forEach(step => {
      allSteps.push({
        recipe,
        step,
        isPrep: step.is_prep_step,
        duration: step.duration_minutes || 5, // Default 5 minutes if not specified
      });
    });
  });

  // Group steps by type
  const prepSteps = allSteps.filter(s => s.isPrep);
  const cookingSteps = allSteps.filter(s => !s.isPrep);

  // Calculate total prep time (can be done in parallel for different recipes)
  const maxPrepTime = Math.max(
    ...scaledRecipes.map(recipe => 
      recipe.scaledSteps
        .filter(step => step.is_prep_step)
        .reduce((total, step) => total + (step.duration_minutes || 5), 0)
    )
  );

  // Calculate cooking timeline working backwards from completion time
  const sessionSteps: Omit<SessionStep, 'id' | 'created_at'>[] = [];
  let currentTime = new Date(targetCompletionTime);

  // Schedule cooking steps in reverse order
  const sortedCookingSteps = cookingSteps.sort((a, b) => {
    // Prioritize steps that can't be done ahead and have dependencies
    const aScore = (a.step.can_be_done_ahead ? 0 : 10) + (a.step.depends_on_steps?.length || 0);
    const bScore = (b.step.can_be_done_ahead ? 0 : 10) + (b.step.depends_on_steps?.length || 0);
    return bScore - aScore;
  });

  sortedCookingSteps.forEach(({ recipe, step }) => {
    const stepEndTime = new Date(currentTime);
    const stepStartTime = subMinutes(stepEndTime, step.duration_minutes || 5);
    
    sessionSteps.unshift({
      session_id: session.id,
      recipe_id: recipe.id,
      step_id: step.id,
      scheduled_start_time: stepStartTime.toISOString(),
      scheduled_end_time: stepEndTime.toISOString(),
      status: 'pending',
    });

    currentTime = stepStartTime;
  });

  // Schedule prep steps before cooking starts
  const cookingStartTime = sessionSteps.length > 0 
    ? new Date(sessionSteps[0].scheduled_start_time)
    : currentTime;
  
  const prepStartTime = subMinutes(cookingStartTime, maxPrepTime);

  // Group prep steps that can be done together
  const consolidatedPrepSteps = consolidatePrepSteps(prepSteps);
  let prepCurrentTime = new Date(prepStartTime);

  consolidatedPrepSteps.forEach(({ title, description, duration, recipes, steps }) => {
    const stepEndTime = addMinutes(prepCurrentTime, duration);
    
    // Create a session step for the consolidated prep
    sessionSteps.unshift({
      session_id: session.id,
      recipe_id: recipes[0].id, // Use first recipe as primary
      step_id: steps[0].id, // Use first step as primary
      scheduled_start_time: prepCurrentTime.toISOString(),
      scheduled_end_time: stepEndTime.toISOString(),
      status: 'pending',
      notes: `Consolidated prep: ${title}. Affects recipes: ${recipes.map(r => r.name).join(', ')}`,
    });

    prepCurrentTime = stepEndTime;
  });

  const totalDuration = Math.ceil(
    (targetCompletionTime.getTime() - prepStartTime.getTime()) / (1000 * 60)
  );

  const timeline: CookingTimeline = {
    session_id: session.id,
    total_duration_minutes: totalDuration,
    prep_phase_duration: maxPrepTime,
    cook_phase_duration: totalDuration - maxPrepTime,
    steps: sessionSteps.map((step, index) => ({
      ...step,
      id: `temp-${index}`, // Temporary ID for UI
    })) as SessionStep[],
    critical_path: findCriticalPath(sessionSteps),
  };

  const shoppingList = aggregateIngredients(scaledRecipes, session.id);

  return {
    timeline,
    sessionSteps,
    shoppingList,
    estimatedTotalTime: totalDuration,
  };
}

/**
 * Consolidate similar prep steps across recipes
 */
function consolidatePrepSteps(prepSteps: Array<{
  recipe: ScaledRecipe;
  step: CookingStep;
  duration: number;
}>) {
  const consolidated: Array<{
    title: string;
    description: string;
    duration: number;
    recipes: ScaledRecipe[];
    steps: CookingStep[];
  }> = [];

  // Group by similar prep activities
  const groups = new Map<string, typeof prepSteps>();
  
  prepSteps.forEach(item => {
    const key = getStepGroupKey(item.step);
    if (!groups.has(key)) {
      groups.set(key, []);
    }
    groups.get(key)!.push(item);
  });

  groups.forEach((items, key) => {
    const maxDuration = Math.max(...items.map(item => item.duration));
    const recipes = items.map(item => item.recipe);
    const steps = items.map(item => item.step);
    
    consolidated.push({
      title: `${key} (${recipes.length} recipes)`,
      description: `Prepare ingredients for: ${recipes.map(r => r.name).join(', ')}`,
      duration: maxDuration,
      recipes,
      steps,
    });
  });

  return consolidated;
}

/**
 * Generate a grouping key for similar prep steps
 */
function getStepGroupKey(step: CookingStep): string {
  const description = step.description.toLowerCase();
  
  if (description.includes('chop') || description.includes('dice') || description.includes('cut')) {
    return 'Chopping & Cutting';
  }
  if (description.includes('mince') || description.includes('garlic')) {
    return 'Mincing';
  }
  if (description.includes('measure') || description.includes('prep')) {
    return 'Measuring & Prep';
  }
  if (description.includes('wash') || description.includes('clean')) {
    return 'Washing & Cleaning';
  }
  
  return 'General Prep';
}

/**
 * Find the critical path (longest sequence) of cooking steps
 */
function findCriticalPath(sessionSteps: Omit<SessionStep, 'id' | 'created_at'>[]): string[] {
  // For now, return all step IDs in chronological order
  // In a more sophisticated implementation, this would analyze dependencies
  return sessionSteps
    .sort((a, b) => new Date(a.scheduled_start_time).getTime() - new Date(b.scheduled_start_time).getTime())
    .map(step => step.step_id);
}

/**
 * Format time for display
 */
export function formatCookingTime(minutes: number): string {
  if (minutes < 60) {
    return `${minutes}m`;
  }
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  return remainingMinutes > 0 ? `${hours}h ${remainingMinutes}m` : `${hours}h`;
}

/**
 * Calculate when to start cooking to finish at a specific time
 */
export function calculateStartTime(targetFinishTime: Date, totalDurationMinutes: number): Date {
  return subMinutes(targetFinishTime, totalDurationMinutes);
}
