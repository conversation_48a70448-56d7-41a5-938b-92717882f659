import type { Recipe, RecipeIngredient, ShoppingListItem, Ingredient } from '@/types';

export interface IngredientConversion {
  fromUnit: string;
  toUnit: string;
  factor: number;
}

export interface AggregatedIngredient {
  ingredient_id: string;
  ingredient?: Ingredient;
  total_quantity: number;
  unit: string;
  recipes: string[]; // Recipe names that use this ingredient
  notes: string[];
  conflicts: IngredientConflict[];
}

export interface IngredientConflict {
  type: 'unit_mismatch' | 'preparation_difference';
  description: string;
  recipes: string[];
  suggestions: string[];
}

// Common unit conversions for cooking
const UNIT_CONVERSIONS: IngredientConversion[] = [
  // Volume conversions
  { fromUnit: 'tablespoons', toUnit: 'cups', factor: 1/16 },
  { fromUnit: 'teaspoons', toUnit: 'tablespoons', factor: 1/3 },
  { fromUnit: 'teaspoons', toUnit: 'cups', factor: 1/48 },
  { fromUnit: 'fluid ounces', toUnit: 'cups', factor: 1/8 },
  { fromUnit: 'pints', toUnit: 'cups', factor: 2 },
  { fromUnit: 'quarts', toUnit: 'cups', factor: 4 },
  
  // Weight conversions
  { fromUnit: 'ounces', toUnit: 'pounds', factor: 1/16 },
  { fromUnit: 'grams', toUnit: 'kilograms', factor: 1/1000 },
  
  // Common cooking conversions
  { fromUnit: 'cloves', toUnit: 'teaspoons', factor: 0.5 }, // garlic
  { fromUnit: 'medium onions', toUnit: 'cups', factor: 1 }, // diced onion
];

/**
 * Aggregate ingredients across multiple recipes with scaling
 */
export function aggregateIngredients(
  recipes: Recipe[],
  servingMultipliers: { [recipeId: string]: number } = {}
): AggregatedIngredient[] {
  const ingredientMap = new Map<string, {
    entries: Array<{
      recipe: Recipe;
      ingredient: RecipeIngredient;
      scaledQuantity: number;
    }>;
  }>();

  // Collect all ingredients from all recipes
  recipes.forEach(recipe => {
    const multiplier = servingMultipliers[recipe.id] || 1;
    
    recipe.ingredients.forEach(ingredient => {
      const key = ingredient.ingredient_id;
      
      if (!ingredientMap.has(key)) {
        ingredientMap.set(key, { entries: [] });
      }
      
      ingredientMap.get(key)!.entries.push({
        recipe,
        ingredient,
        scaledQuantity: ingredient.quantity * multiplier,
      });
    });
  });

  // Process each ingredient group
  const aggregatedIngredients: AggregatedIngredient[] = [];

  ingredientMap.forEach((data, ingredientId) => {
    const aggregated = processIngredientGroup(ingredientId, data.entries);
    aggregatedIngredients.push(aggregated);
  });

  return aggregatedIngredients.sort((a, b) => {
    // Sort by ingredient name
    const nameA = a.ingredient?.name || '';
    const nameB = b.ingredient?.name || '';
    return nameA.localeCompare(nameB);
  });
}

/**
 * Process a group of the same ingredient from different recipes
 */
function processIngredientGroup(
  ingredientId: string,
  entries: Array<{
    recipe: Recipe;
    ingredient: RecipeIngredient;
    scaledQuantity: number;
  }>
): AggregatedIngredient {
  const firstEntry = entries[0];
  const ingredient = firstEntry.ingredient.ingredient;
  const recipes = entries.map(e => e.recipe.name);
  const notes = entries
    .map(e => e.ingredient.notes)
    .filter(Boolean)
    .filter((note, index, arr) => arr.indexOf(note) === index); // Remove duplicates

  // Group by unit to handle unit conversions
  const unitGroups = new Map<string, number>();
  const conflicts: IngredientConflict[] = [];

  entries.forEach(entry => {
    const unit = normalizeUnit(entry.ingredient.unit);
    const currentQuantity = unitGroups.get(unit) || 0;
    unitGroups.set(unit, currentQuantity + entry.scaledQuantity);
  });

  // Try to consolidate units
  const { totalQuantity, finalUnit, unitConflicts } = consolidateUnits(
    Array.from(unitGroups.entries()),
    ingredient?.unit || firstEntry.ingredient.unit
  );

  conflicts.push(...unitConflicts);

  // Check for preparation conflicts
  const prepConflicts = findPreparationConflicts(entries);
  conflicts.push(...prepConflicts);

  return {
    ingredient_id: ingredientId,
    ingredient,
    total_quantity: totalQuantity,
    unit: finalUnit,
    recipes,
    notes,
    conflicts,
  };
}

/**
 * Consolidate different units for the same ingredient
 */
function consolidateUnits(
  unitQuantities: Array<[string, number]>,
  preferredUnit: string
): {
  totalQuantity: number;
  finalUnit: string;
  unitConflicts: IngredientConflict[];
} {
  if (unitQuantities.length === 1) {
    return {
      totalQuantity: unitQuantities[0][1],
      finalUnit: unitQuantities[0][0],
      unitConflicts: [],
    };
  }

  const conflicts: IngredientConflict[] = [];
  
  // Try to convert everything to the preferred unit
  let totalInPreferredUnit = 0;
  let canConvertAll = true;
  const unconvertibleUnits: string[] = [];

  for (const [unit, quantity] of unitQuantities) {
    if (unit === preferredUnit) {
      totalInPreferredUnit += quantity;
    } else {
      const conversion = findUnitConversion(unit, preferredUnit);
      if (conversion) {
        totalInPreferredUnit += quantity * conversion.factor;
      } else {
        canConvertAll = false;
        unconvertibleUnits.push(unit);
      }
    }
  }

  if (canConvertAll) {
    return {
      totalQuantity: totalInPreferredUnit,
      finalUnit: preferredUnit,
      unitConflicts: [],
    };
  }

  // If we can't convert all units, use the most common unit
  const unitCounts = new Map<string, number>();
  unitQuantities.forEach(([unit]) => {
    unitCounts.set(unit, (unitCounts.get(unit) || 0) + 1);
  });

  const mostCommonUnit = Array.from(unitCounts.entries())
    .sort((a, b) => b[1] - a[1])[0][0];

  // Calculate total in most common unit
  let totalInCommonUnit = 0;
  const conflictingUnits: string[] = [];

  for (const [unit, quantity] of unitQuantities) {
    if (unit === mostCommonUnit) {
      totalInCommonUnit += quantity;
    } else {
      const conversion = findUnitConversion(unit, mostCommonUnit);
      if (conversion) {
        totalInCommonUnit += quantity * conversion.factor;
      } else {
        conflictingUnits.push(unit);
        // For now, just add the quantity as-is (not ideal, but better than losing it)
        totalInCommonUnit += quantity;
      }
    }
  }

  if (conflictingUnits.length > 0) {
    conflicts.push({
      type: 'unit_mismatch',
      description: `Cannot convert between units: ${conflictingUnits.join(', ')} and ${mostCommonUnit}`,
      recipes: [], // Would need to track which recipes use which units
      suggestions: [
        'Consider standardizing units across recipes',
        'Manually verify quantities when shopping',
      ],
    });
  }

  return {
    totalQuantity: totalInCommonUnit,
    finalUnit: mostCommonUnit,
    unitConflicts: conflicts,
  };
}

/**
 * Find preparation conflicts (different prep instructions for same ingredient)
 */
function findPreparationConflicts(
  entries: Array<{
    recipe: Recipe;
    ingredient: RecipeIngredient;
    scaledQuantity: number;
  }>
): IngredientConflict[] {
  const conflicts: IngredientConflict[] = [];
  const prepNotes = entries
    .map(e => ({ note: e.ingredient.notes, recipe: e.recipe.name }))
    .filter(item => item.note);

  if (prepNotes.length > 1) {
    const uniqueNotes = Array.from(new Set(prepNotes.map(p => p.note)));
    
    if (uniqueNotes.length > 1) {
      conflicts.push({
        type: 'preparation_difference',
        description: `Different preparation methods: ${uniqueNotes.join(', ')}`,
        recipes: prepNotes.map(p => p.recipe),
        suggestions: [
          'Prepare ingredient according to the most complex requirement',
          'Prepare portions separately for different recipes',
        ],
      });
    }
  }

  return conflicts;
}

/**
 * Find unit conversion between two units
 */
function findUnitConversion(fromUnit: string, toUnit: string): IngredientConversion | null {
  const normalizedFrom = normalizeUnit(fromUnit);
  const normalizedTo = normalizeUnit(toUnit);

  // Direct conversion
  const direct = UNIT_CONVERSIONS.find(
    conv => normalizeUnit(conv.fromUnit) === normalizedFrom && 
            normalizeUnit(conv.toUnit) === normalizedTo
  );
  
  if (direct) return direct;

  // Reverse conversion
  const reverse = UNIT_CONVERSIONS.find(
    conv => normalizeUnit(conv.fromUnit) === normalizedTo && 
            normalizeUnit(conv.toUnit) === normalizedFrom
  );
  
  if (reverse) {
    return {
      fromUnit: normalizedFrom,
      toUnit: normalizedTo,
      factor: 1 / reverse.factor,
    };
  }

  // Multi-step conversion (simple case: A -> B -> C)
  for (const intermediate of UNIT_CONVERSIONS) {
    if (normalizeUnit(intermediate.fromUnit) === normalizedFrom) {
      const secondStep = findUnitConversion(intermediate.toUnit, normalizedTo);
      if (secondStep) {
        return {
          fromUnit: normalizedFrom,
          toUnit: normalizedTo,
          factor: intermediate.factor * secondStep.factor,
        };
      }
    }
  }

  return null;
}

/**
 * Normalize unit names for comparison
 */
function normalizeUnit(unit: string): string {
  return unit.toLowerCase().trim()
    .replace(/s$/, '') // Remove plural 's'
    .replace(/\./g, ''); // Remove periods
}

/**
 * Convert aggregated ingredients to shopping list items
 */
export function createShoppingListItems(
  sessionId: string,
  aggregatedIngredients: AggregatedIngredient[]
): Omit<ShoppingListItem, 'id' | 'created_at'>[] {
  return aggregatedIngredients.map(agg => ({
    session_id: sessionId,
    ingredient_id: agg.ingredient_id,
    total_quantity: agg.total_quantity,
    unit: agg.unit,
    is_purchased: false,
    notes: agg.notes.length > 0 ? agg.notes.join(', ') : undefined,
  }));
}

/**
 * Format quantity for display
 */
export function formatQuantity(quantity: number, unit: string): string {
  // Handle fractions for common cooking measurements
  if (unit.toLowerCase().includes('cup') && quantity < 1) {
    const fractions = [
      { decimal: 0.125, display: '1/8' },
      { decimal: 0.25, display: '1/4' },
      { decimal: 0.333, display: '1/3' },
      { decimal: 0.5, display: '1/2' },
      { decimal: 0.667, display: '2/3' },
      { decimal: 0.75, display: '3/4' },
    ];
    
    const closestFraction = fractions.find(f => Math.abs(f.decimal - quantity) < 0.05);
    if (closestFraction) {
      return closestFraction.display;
    }
  }

  // Round to reasonable precision
  if (quantity < 10) {
    return quantity.toFixed(1).replace(/\.0$/, '');
  } else {
    return Math.round(quantity).toString();
  }
}
